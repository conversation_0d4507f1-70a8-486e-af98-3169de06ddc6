DEBUG - 2025-05-28 06:49:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:49:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:49:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:50:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:50:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:50:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:50:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:51:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:52:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:52:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:52:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:52:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:52:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:52:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:52:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:53:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:53:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:53:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:53:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:53:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:53:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:53:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:53:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:53:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:55:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:55:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:55:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:55:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:58:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:59:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 06:59:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-05-28 07:01:36 --> mysqli_sql_exception: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'NOT NULL DEFAULT 'participant',
	created_at datetime default current_timestam...' at line 5 in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('CREATE TABLE `u...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('CREATE TABLE `u...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('CREATE TABLE `u...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\Forge.php(570): CodeIgniter\Database\BaseConnection->query('CREATE TABLE `u...')
#4 C:\sertifikat\myapp\app\Database\Migrations\2025-05-28-070000_CreateUsersTable.php(35): CodeIgniter\Database\Forge->createTable('users')
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(876): App\Database\Migrations\CreateUsersTable->up()
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(182): CodeIgniter\Database\MigrationRunner->migrate('up', Object(stdClass))
#7 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Commands\Database\Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest(NULL)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Commands.php(74): CodeIgniter\Commands\Database\Migrate->run(Array)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\BaseCommand.php(119): CodeIgniter\CLI\Commands->run('migrate', Array)
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Commands\Database\MigrateRefresh.php(87): CodeIgniter\CLI\BaseCommand->call('migrate', Array)
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Commands.php(74): CodeIgniter\Commands\Database\MigrateRefresh->run(Array)
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Console.php(47): CodeIgniter\CLI\Commands->run('migrate:refresh', Array)
#13 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(360): CodeIgniter\CLI\Console->run()
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#15 C:\sertifikat\myapp\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#16 {main}
DEBUG - 2025-05-28 07:03:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:03:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:03:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:03:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:03:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-05-28 07:05:00 --> mysqli_sql_exception: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'NOT NULL DEFAULT 'participant',
	created_at datetime default current_timestam...' at line 7 in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('CREATE TABLE `u...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('CREATE TABLE `u...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('CREATE TABLE `u...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\Forge.php(570): CodeIgniter\Database\BaseConnection->query('CREATE TABLE `u...')
#4 C:\sertifikat\myapp\app\Database\Migrations\2025-05-28-070000_CreateUsersTable.php(45): CodeIgniter\Database\Forge->createTable('users')
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(876): App\Database\Migrations\CreateUsersTable->up()
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(182): CodeIgniter\Database\MigrationRunner->migrate('up', Object(stdClass))
#7 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Commands\Database\Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest(NULL)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Commands.php(74): CodeIgniter\Commands\Database\Migrate->run(Array)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\BaseCommand.php(119): CodeIgniter\CLI\Commands->run('migrate', Array)
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Commands\Database\MigrateRefresh.php(87): CodeIgniter\CLI\BaseCommand->call('migrate', Array)
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Commands.php(74): CodeIgniter\Commands\Database\MigrateRefresh->run(Array)
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Console.php(47): CodeIgniter\CLI\Commands->run('migrate:refresh', Array)
#13 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(360): CodeIgniter\CLI\Console->run()
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#15 C:\sertifikat\myapp\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#16 {main}
DEBUG - 2025-05-28 07:06:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:06:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:06:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:06:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:07:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-05-28 07:10:56 --> mysqli_sql_exception: Table 'event_management.users' doesn't exist in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT 1\nFROM `...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT 1\nFROM `...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT 1\nFROM `...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1\nFROM `...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Validation\Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Validation\StrictRules\Rules.php(187): CodeIgniter\Validation\Rules->is_unique('daffaelmerah@gm...', 'users.email', Array)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Validation\Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('daffaelmerah@gm...', 'users.email', Array, NULL, 'email')
#7 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('email', 'email', 'daffaelmerah@gm...', Array, Array, 'email')
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Controller.php(138): CodeIgniter\Validation\Validation->run()
#9 C:\sertifikat\myapp\app\Controllers\Auth.php(73): CodeIgniter\Controller->validate(Array)
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptRegister()
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#13 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#15 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#16 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#17 {main}
CRITICAL - 2025-05-28 07:10:56 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'event_management.users' doesn't exist
[Method: POST, Route: auth/attemptRegister]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 2 SYSTEMPATH\Validation\Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\Validation\StrictRules\Rules.php(187): CodeIgniter\Validation\Rules->is_unique('<EMAIL>', 'users.email', [...])
 4 SYSTEMPATH\Validation\Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('<EMAIL>', 'users.email', [...], null, 'email')
 5 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('email', 'email', '<EMAIL>', [...], [...], 'email')
 6 SYSTEMPATH\Controller.php(138): CodeIgniter\Validation\Validation->run()
 7 APPPATH\Controllers\Auth.php(73): CodeIgniter\Controller->validate([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptRegister()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-28 07:10:56 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'event_management.users' doesn't exist
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT 1
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT 1
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 4 SYSTEMPATH\Validation\Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\Validation\StrictRules\Rules.php(187): CodeIgniter\Validation\Rules->is_unique('<EMAIL>', 'users.email', [...])
 6 SYSTEMPATH\Validation\Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('<EMAIL>', 'users.email', [...], null, 'email')
 7 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('email', 'email', '<EMAIL>', [...], [...], 'email')
 8 SYSTEMPATH\Controller.php(138): CodeIgniter\Validation\Validation->run()
 9 APPPATH\Controllers\Auth.php(73): CodeIgniter\Controller->validate([...])
10 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptRegister()
11 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
12 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
13 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
15 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
16 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-28 07:10:56 --> [Caused by] mysqli_sql_exception: Table 'event_management.users' doesn't exist
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT 1
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT 1
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT 1
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 5 SYSTEMPATH\Validation\Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\Validation\StrictRules\Rules.php(187): CodeIgniter\Validation\Rules->is_unique('<EMAIL>', 'users.email', [...])
 7 SYSTEMPATH\Validation\Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('<EMAIL>', 'users.email', [...], null, 'email')
 8 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('email', 'email', '<EMAIL>', [...], [...], 'email')
 9 SYSTEMPATH\Controller.php(138): CodeIgniter\Validation\Validation->run()
10 APPPATH\Controllers\Auth.php(73): CodeIgniter\Controller->validate([...])
11 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptRegister()
12 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
13 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
14 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
15 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
16 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
17 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
ERROR - 2025-05-28 07:11:22 --> mysqli_sql_exception: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'NOT NULL DEFAULT 'participant',
	created_at datetime default current_timestam...' at line 7 in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('CREATE TABLE `u...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('CREATE TABLE `u...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('CREATE TABLE `u...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\Forge.php(570): CodeIgniter\Database\BaseConnection->query('CREATE TABLE `u...')
#4 C:\sertifikat\myapp\app\Database\Migrations\2025-05-28-070000_CreateUsersTable.php(45): CodeIgniter\Database\Forge->createTable('users')
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(876): App\Database\Migrations\CreateUsersTable->up()
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(182): CodeIgniter\Database\MigrationRunner->migrate('up', Object(stdClass))
#7 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Commands\Database\Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest(NULL)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Commands.php(74): CodeIgniter\Commands\Database\Migrate->run(Array)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Console.php(47): CodeIgniter\CLI\Commands->run('migrate', Array)
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(360): CodeIgniter\CLI\Console->run()
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#12 C:\sertifikat\myapp\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#13 {main}
ERROR - 2025-05-28 07:11:34 --> mysqli_sql_exception: Table 'event_management.users' doesn't exist in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT 1\nFROM `...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT 1\nFROM `...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT 1\nFROM `...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1\nFROM `...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Validation\Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Validation\StrictRules\Rules.php(187): CodeIgniter\Validation\Rules->is_unique('daffaelmerah@gm...', 'users.email', Array)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Validation\Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('daffaelmerah@gm...', 'users.email', Array, NULL, 'email')
#7 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('email', 'email', 'daffaelmerah@gm...', Array, Array, 'email')
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Controller.php(138): CodeIgniter\Validation\Validation->run()
#9 C:\sertifikat\myapp\app\Controllers\Auth.php(73): CodeIgniter\Controller->validate(Array)
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptRegister()
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#13 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#15 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#16 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#17 {main}
CRITICAL - 2025-05-28 07:11:34 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'event_management.users' doesn't exist
[Method: POST, Route: auth/attemptRegister]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 2 SYSTEMPATH\Validation\Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\Validation\StrictRules\Rules.php(187): CodeIgniter\Validation\Rules->is_unique('<EMAIL>', 'users.email', [...])
 4 SYSTEMPATH\Validation\Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('<EMAIL>', 'users.email', [...], null, 'email')
 5 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('email', 'email', '<EMAIL>', [...], [...], 'email')
 6 SYSTEMPATH\Controller.php(138): CodeIgniter\Validation\Validation->run()
 7 APPPATH\Controllers\Auth.php(73): CodeIgniter\Controller->validate([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptRegister()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-28 07:11:34 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'event_management.users' doesn't exist
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT 1
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT 1
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 4 SYSTEMPATH\Validation\Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\Validation\StrictRules\Rules.php(187): CodeIgniter\Validation\Rules->is_unique('<EMAIL>', 'users.email', [...])
 6 SYSTEMPATH\Validation\Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('<EMAIL>', 'users.email', [...], null, 'email')
 7 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('email', 'email', '<EMAIL>', [...], [...], 'email')
 8 SYSTEMPATH\Controller.php(138): CodeIgniter\Validation\Validation->run()
 9 APPPATH\Controllers\Auth.php(73): CodeIgniter\Controller->validate([...])
10 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptRegister()
11 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
12 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
13 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
15 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
16 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-28 07:11:34 --> [Caused by] mysqli_sql_exception: Table 'event_management.users' doesn't exist
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT 1
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT 1
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT 1
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 5 SYSTEMPATH\Validation\Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\Validation\StrictRules\Rules.php(187): CodeIgniter\Validation\Rules->is_unique('<EMAIL>', 'users.email', [...])
 7 SYSTEMPATH\Validation\Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('<EMAIL>', 'users.email', [...], null, 'email')
 8 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('email', 'email', '<EMAIL>', [...], [...], 'email')
 9 SYSTEMPATH\Controller.php(138): CodeIgniter\Validation\Validation->run()
10 APPPATH\Controllers\Auth.php(73): CodeIgniter\Controller->validate([...])
11 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptRegister()
12 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
13 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
14 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
15 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
16 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
17 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
ERROR - 2025-05-28 07:12:05 --> mysqli_sql_exception: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'NOT NULL DEFAULT 'participant',
	created_at datetime default current_timestam...' at line 7 in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('CREATE TABLE `u...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('CREATE TABLE `u...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('CREATE TABLE `u...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\Forge.php(570): CodeIgniter\Database\BaseConnection->query('CREATE TABLE `u...')
#4 C:\sertifikat\myapp\app\Database\Migrations\2025-05-28-070000_CreateUsersTable.php(45): CodeIgniter\Database\Forge->createTable('users')
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(876): App\Database\Migrations\CreateUsersTable->up()
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(182): CodeIgniter\Database\MigrationRunner->migrate('up', Object(stdClass))
#7 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Commands\Database\Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest(NULL)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Commands.php(74): CodeIgniter\Commands\Database\Migrate->run(Array)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\BaseCommand.php(119): CodeIgniter\CLI\Commands->run('migrate', Array)
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Commands\Database\MigrateRefresh.php(87): CodeIgniter\CLI\BaseCommand->call('migrate', Array)
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Commands.php(74): CodeIgniter\Commands\Database\MigrateRefresh->run(Array)
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Console.php(47): CodeIgniter\CLI\Commands->run('migrate:refresh', Array)
#13 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(360): CodeIgniter\CLI\Console->run()
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#15 C:\sertifikat\myapp\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#16 {main}
ERROR - 2025-05-28 07:12:35 --> mysqli_sql_exception: Table 'event_management.users' doesn't exist in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT 1\nFROM `...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT 1\nFROM `...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT 1\nFROM `...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1\nFROM `...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Validation\Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Validation\StrictRules\Rules.php(187): CodeIgniter\Validation\Rules->is_unique('badrulatok@gmai...', 'users.email', Array)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Validation\Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('badrulatok@gmai...', 'users.email', Array, NULL, 'email')
#7 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('email', 'email', 'badrulatok@gmai...', Array, Array, 'email')
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Controller.php(138): CodeIgniter\Validation\Validation->run()
#9 C:\sertifikat\myapp\app\Controllers\Auth.php(73): CodeIgniter\Controller->validate(Array)
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptRegister()
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#13 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#15 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#16 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#17 {main}
CRITICAL - 2025-05-28 07:12:35 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'event_management.users' doesn't exist
[Method: POST, Route: auth/attemptRegister]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 2 SYSTEMPATH\Validation\Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\Validation\StrictRules\Rules.php(187): CodeIgniter\Validation\Rules->is_unique('<EMAIL>', 'users.email', [...])
 4 SYSTEMPATH\Validation\Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('<EMAIL>', 'users.email', [...], null, 'email')
 5 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('email', 'email', '<EMAIL>', [...], [...], 'email')
 6 SYSTEMPATH\Controller.php(138): CodeIgniter\Validation\Validation->run()
 7 APPPATH\Controllers\Auth.php(73): CodeIgniter\Controller->validate([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptRegister()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-28 07:12:35 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'event_management.users' doesn't exist
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT 1
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT 1
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 4 SYSTEMPATH\Validation\Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\Validation\StrictRules\Rules.php(187): CodeIgniter\Validation\Rules->is_unique('<EMAIL>', 'users.email', [...])
 6 SYSTEMPATH\Validation\Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('<EMAIL>', 'users.email', [...], null, 'email')
 7 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('email', 'email', '<EMAIL>', [...], [...], 'email')
 8 SYSTEMPATH\Controller.php(138): CodeIgniter\Validation\Validation->run()
 9 APPPATH\Controllers\Auth.php(73): CodeIgniter\Controller->validate([...])
10 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptRegister()
11 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
12 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
13 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
15 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
16 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-28 07:12:35 --> [Caused by] mysqli_sql_exception: Table 'event_management.users' doesn't exist
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT 1
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT 1
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT 1
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 5 SYSTEMPATH\Validation\Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\Validation\StrictRules\Rules.php(187): CodeIgniter\Validation\Rules->is_unique('<EMAIL>', 'users.email', [...])
 7 SYSTEMPATH\Validation\Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('<EMAIL>', 'users.email', [...], null, 'email')
 8 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('email', 'email', '<EMAIL>', [...], [...], 'email')
 9 SYSTEMPATH\Controller.php(138): CodeIgniter\Validation\Validation->run()
10 APPPATH\Controllers\Auth.php(73): CodeIgniter\Controller->validate([...])
11 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptRegister()
12 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
13 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
14 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
15 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
16 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
17 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
ERROR - 2025-05-28 07:12:49 --> mysqli_sql_exception: Table 'event_management.users' doesn't exist in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT 1\nFROM `...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT 1\nFROM `...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT 1\nFROM `...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1\nFROM `...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Validation\Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Validation\StrictRules\Rules.php(187): CodeIgniter\Validation\Rules->is_unique('badrulatok@gmai...', 'users.email', Array)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Validation\Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('badrulatok@gmai...', 'users.email', Array, NULL, 'email')
#7 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('email', 'email', 'badrulatok@gmai...', Array, Array, 'email')
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Controller.php(138): CodeIgniter\Validation\Validation->run()
#9 C:\sertifikat\myapp\app\Controllers\Auth.php(73): CodeIgniter\Controller->validate(Array)
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptRegister()
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#13 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#15 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#16 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#17 {main}
CRITICAL - 2025-05-28 07:12:49 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'event_management.users' doesn't exist
[Method: POST, Route: auth/attemptRegister]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 2 SYSTEMPATH\Validation\Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\Validation\StrictRules\Rules.php(187): CodeIgniter\Validation\Rules->is_unique('<EMAIL>', 'users.email', [...])
 4 SYSTEMPATH\Validation\Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('<EMAIL>', 'users.email', [...], null, 'email')
 5 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('email', 'email', '<EMAIL>', [...], [...], 'email')
 6 SYSTEMPATH\Controller.php(138): CodeIgniter\Validation\Validation->run()
 7 APPPATH\Controllers\Auth.php(73): CodeIgniter\Controller->validate([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptRegister()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-28 07:12:49 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'event_management.users' doesn't exist
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT 1
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT 1
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 4 SYSTEMPATH\Validation\Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\Validation\StrictRules\Rules.php(187): CodeIgniter\Validation\Rules->is_unique('<EMAIL>', 'users.email', [...])
 6 SYSTEMPATH\Validation\Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('<EMAIL>', 'users.email', [...], null, 'email')
 7 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('email', 'email', '<EMAIL>', [...], [...], 'email')
 8 SYSTEMPATH\Controller.php(138): CodeIgniter\Validation\Validation->run()
 9 APPPATH\Controllers\Auth.php(73): CodeIgniter\Controller->validate([...])
10 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptRegister()
11 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
12 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
13 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
15 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
16 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-28 07:12:49 --> [Caused by] mysqli_sql_exception: Table 'event_management.users' doesn't exist
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT 1
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT 1
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT 1
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 5 SYSTEMPATH\Validation\Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\Validation\StrictRules\Rules.php(187): CodeIgniter\Validation\Rules->is_unique('<EMAIL>', 'users.email', [...])
 7 SYSTEMPATH\Validation\Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('<EMAIL>', 'users.email', [...], null, 'email')
 8 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('email', 'email', '<EMAIL>', [...], [...], 'email')
 9 SYSTEMPATH\Controller.php(138): CodeIgniter\Validation\Validation->run()
10 APPPATH\Controllers\Auth.php(73): CodeIgniter\Controller->validate([...])
11 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptRegister()
12 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
13 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
14 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
15 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
16 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
17 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
ERROR - 2025-05-28 07:13:27 --> mysqli_sql_exception: Table 'event_management.users' doesn't exist in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT 1\nFROM `...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT 1\nFROM `...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT 1\nFROM `...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1\nFROM `...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Validation\Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Validation\StrictRules\Rules.php(187): CodeIgniter\Validation\Rules->is_unique('badrulatok@gmai...', 'users.email', Array)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Validation\Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('badrulatok@gmai...', 'users.email', Array, NULL, 'email')
#7 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('email', 'email', 'badrulatok@gmai...', Array, Array, 'email')
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Controller.php(138): CodeIgniter\Validation\Validation->run()
#9 C:\sertifikat\myapp\app\Controllers\Auth.php(73): CodeIgniter\Controller->validate(Array)
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptRegister()
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#13 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#15 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#16 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#17 {main}
CRITICAL - 2025-05-28 07:13:27 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'event_management.users' doesn't exist
[Method: POST, Route: auth/attemptRegister]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 2 SYSTEMPATH\Validation\Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\Validation\StrictRules\Rules.php(187): CodeIgniter\Validation\Rules->is_unique('<EMAIL>', 'users.email', [...])
 4 SYSTEMPATH\Validation\Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('<EMAIL>', 'users.email', [...], null, 'email')
 5 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('email', 'email', '<EMAIL>', [...], [...], 'email')
 6 SYSTEMPATH\Controller.php(138): CodeIgniter\Validation\Validation->run()
 7 APPPATH\Controllers\Auth.php(73): CodeIgniter\Controller->validate([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptRegister()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-28 07:13:27 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'event_management.users' doesn't exist
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT 1
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT 1
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 4 SYSTEMPATH\Validation\Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\Validation\StrictRules\Rules.php(187): CodeIgniter\Validation\Rules->is_unique('<EMAIL>', 'users.email', [...])
 6 SYSTEMPATH\Validation\Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('<EMAIL>', 'users.email', [...], null, 'email')
 7 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('email', 'email', '<EMAIL>', [...], [...], 'email')
 8 SYSTEMPATH\Controller.php(138): CodeIgniter\Validation\Validation->run()
 9 APPPATH\Controllers\Auth.php(73): CodeIgniter\Controller->validate([...])
10 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptRegister()
11 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
12 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
13 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
15 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
16 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-28 07:13:27 --> [Caused by] mysqli_sql_exception: Table 'event_management.users' doesn't exist
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT 1
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT 1
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT 1
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 5 SYSTEMPATH\Validation\Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\Validation\StrictRules\Rules.php(187): CodeIgniter\Validation\Rules->is_unique('<EMAIL>', 'users.email', [...])
 7 SYSTEMPATH\Validation\Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('<EMAIL>', 'users.email', [...], null, 'email')
 8 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('email', 'email', '<EMAIL>', [...], [...], 'email')
 9 SYSTEMPATH\Controller.php(138): CodeIgniter\Validation\Validation->run()
10 APPPATH\Controllers\Auth.php(73): CodeIgniter\Controller->validate([...])
11 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptRegister()
12 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
13 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
14 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
15 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
16 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
17 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-28 07:13:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-05-28 07:13:44 --> mysqli_sql_exception: Table 'event_management.users' doesn't exist in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\BaseModel.php(717): CodeIgniter\Model->doFirst()
#6 C:\sertifikat\myapp\app\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
#7 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#14 {main}
CRITICAL - 2025-05-28 07:13:44 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'event_management.users' doesn't exist
[Method: POST, Route: auth/attemptLogin]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 2 SYSTEMPATH\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(717): CodeIgniter\Model->doFirst()
 4 APPPATH\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-28 07:13:44 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'event_management.users' doesn't exist
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 4 SYSTEMPATH\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(717): CodeIgniter\Model->doFirst()
 6 APPPATH\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
 7 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
 8 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
 9 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
11 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
12 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
13 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-28 07:13:44 --> [Caused by] mysqli_sql_exception: Table 'event_management.users' doesn't exist
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 5 SYSTEMPATH\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(717): CodeIgniter\Model->doFirst()
 7 APPPATH\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-28 07:13:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-05-28 07:14:13 --> mysqli_sql_exception: Table 'event_management.users' doesn't exist in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT 1\nFROM `...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT 1\nFROM `...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT 1\nFROM `...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1\nFROM `...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Validation\Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Validation\StrictRules\Rules.php(187): CodeIgniter\Validation\Rules->is_unique('<EMAIL>...', 'users.email', Array)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Validation\Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('<EMAIL>...', 'users.email', Array, NULL, 'email')
#7 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('email', 'email', '<EMAIL>...', Array, Array, 'email')
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Controller.php(138): CodeIgniter\Validation\Validation->run()
#9 C:\sertifikat\myapp\app\Controllers\Auth.php(73): CodeIgniter\Controller->validate(Array)
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptRegister()
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#13 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#15 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#16 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#17 {main}
CRITICAL - 2025-05-28 07:14:13 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'event_management.users' doesn't exist
[Method: POST, Route: auth/attemptRegister]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 2 SYSTEMPATH\Validation\Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\Validation\StrictRules\Rules.php(187): CodeIgniter\Validation\Rules->is_unique('<EMAIL>', 'users.email', [...])
 4 SYSTEMPATH\Validation\Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('<EMAIL>', 'users.email', [...], null, 'email')
 5 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('email', 'email', '<EMAIL>', [...], [...], 'email')
 6 SYSTEMPATH\Controller.php(138): CodeIgniter\Validation\Validation->run()
 7 APPPATH\Controllers\Auth.php(73): CodeIgniter\Controller->validate([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptRegister()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-28 07:14:13 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'event_management.users' doesn't exist
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT 1
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT 1
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 4 SYSTEMPATH\Validation\Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\Validation\StrictRules\Rules.php(187): CodeIgniter\Validation\Rules->is_unique('<EMAIL>', 'users.email', [...])
 6 SYSTEMPATH\Validation\Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('<EMAIL>', 'users.email', [...], null, 'email')
 7 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('email', 'email', '<EMAIL>', [...], [...], 'email')
 8 SYSTEMPATH\Controller.php(138): CodeIgniter\Validation\Validation->run()
 9 APPPATH\Controllers\Auth.php(73): CodeIgniter\Controller->validate([...])
10 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptRegister()
11 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
12 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
13 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
15 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
16 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-28 07:14:13 --> [Caused by] mysqli_sql_exception: Table 'event_management.users' doesn't exist
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT 1
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT 1
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT 1
FROM `users`
WHERE `email` = \'<EMAIL>\'
 LIMIT 1')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT 1
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 5 SYSTEMPATH\Validation\Rules.php(175): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\Validation\StrictRules\Rules.php(187): CodeIgniter\Validation\Rules->is_unique('<EMAIL>', 'users.email', [...])
 7 SYSTEMPATH\Validation\Validation.php(336): CodeIgniter\Validation\StrictRules\Rules->is_unique('<EMAIL>', 'users.email', [...], null, 'email')
 8 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('email', 'email', '<EMAIL>', [...], [...], 'email')
 9 SYSTEMPATH\Controller.php(138): CodeIgniter\Validation\Validation->run()
10 APPPATH\Controllers\Auth.php(73): CodeIgniter\Controller->validate([...])
11 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptRegister()
12 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
13 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
14 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
15 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
16 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
17 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
ERROR - 2025-05-28 07:14:42 --> mysqli_sql_exception: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'NOT NULL DEFAULT 'participant',
	created_at datetime default current_timestam...' at line 7 in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('CREATE TABLE `u...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('CREATE TABLE `u...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('CREATE TABLE `u...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\Forge.php(570): CodeIgniter\Database\BaseConnection->query('CREATE TABLE `u...')
#4 C:\sertifikat\myapp\app\Database\Migrations\2025-05-28-070000_CreateUsersTable.php(45): CodeIgniter\Database\Forge->createTable('users')
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(876): App\Database\Migrations\CreateUsersTable->up()
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(182): CodeIgniter\Database\MigrationRunner->migrate('up', Object(stdClass))
#7 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Commands\Database\Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest(NULL)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Commands.php(74): CodeIgniter\Commands\Database\Migrate->run(Array)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Console.php(47): CodeIgniter\CLI\Commands->run('migrate', Array)
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(360): CodeIgniter\CLI\Console->run()
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#12 C:\sertifikat\myapp\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#13 {main}
ERROR - 2025-05-28 07:15:15 --> mysqli_sql_exception: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'NOT NULL DEFAULT 'participant',
	created_at datetime default current_timestam...' at line 7 in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('CREATE TABLE `u...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('CREATE TABLE `u...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('CREATE TABLE `u...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\Forge.php(570): CodeIgniter\Database\BaseConnection->query('CREATE TABLE `u...')
#4 C:\sertifikat\myapp\app\Database\Migrations\2025-05-28-070000_CreateUsersTable.php(45): CodeIgniter\Database\Forge->createTable('users')
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(876): App\Database\Migrations\CreateUsersTable->up()
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(182): CodeIgniter\Database\MigrationRunner->migrate('up', Object(stdClass))
#7 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Commands\Database\Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest(NULL)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Commands.php(74): CodeIgniter\Commands\Database\Migrate->run(Array)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Console.php(47): CodeIgniter\CLI\Commands->run('migrate', Array)
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(360): CodeIgniter\CLI\Console->run()
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#12 C:\sertifikat\myapp\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#13 {main}
ERROR - 2025-05-28 07:15:33 --> mysqli_sql_exception: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'NOT NULL DEFAULT 'participant',
	created_at datetime default current_timestam...' at line 7 in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('CREATE TABLE `u...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('CREATE TABLE `u...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('CREATE TABLE `u...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\Forge.php(570): CodeIgniter\Database\BaseConnection->query('CREATE TABLE `u...')
#4 C:\sertifikat\myapp\app\Database\Migrations\2025-05-28-070000_CreateUsersTable.php(45): CodeIgniter\Database\Forge->createTable('users')
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(876): App\Database\Migrations\CreateUsersTable->up()
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(182): CodeIgniter\Database\MigrationRunner->migrate('up', Object(stdClass))
#7 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Commands\Database\Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest(NULL)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Commands.php(74): CodeIgniter\Commands\Database\Migrate->run(Array)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Console.php(47): CodeIgniter\CLI\Commands->run('migrate', Array)
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(360): CodeIgniter\CLI\Console->run()
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#12 C:\sertifikat\myapp\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#13 {main}
DEBUG - 2025-05-28 07:16:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:16:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:16:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:16:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:16:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:17:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:17:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:17:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:18:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:18:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:18:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:19:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:19:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:20:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:20:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:20:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:20:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:20:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:20:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:20:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:20:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:20:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:20:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:20:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:21:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:21:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:21:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:21:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:21:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:23:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:23:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:23:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:23:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:23:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:23:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:23:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:24:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:24:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:24:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:24:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:32:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:32:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:32:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:32:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:32:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:32:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:32:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:32:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:32:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:32:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:32:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:32:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:32:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:32:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:32:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:32:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:32:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:32:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:32:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:32:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:32:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:32:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:32:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:33:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:33:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:33:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:33:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:33:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:33:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:33:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:33:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:33:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:33:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:33:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:33:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:33:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:33:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:35:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:35:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:35:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:35:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:35:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:35:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:35:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:35:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:35:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:35:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:35:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:37:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:37:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:37:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:37:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:37:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:38:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:38:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:38:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:38:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:38:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:38:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:38:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:38:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:38:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:38:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:39:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:39:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:39:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:39:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:40:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:40:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:40:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:41:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:41:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:45:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-28 07:45:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
