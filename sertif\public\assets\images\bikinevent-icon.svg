<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Circle with Gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="calendarGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="certificateGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffd700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffed4e;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="32" cy="32" r="30" fill="url(#bgGradient)" stroke="#ffffff" stroke-width="2"/>
  
  <!-- Calendar Base -->
  <rect x="14" y="18" width="24" height="20" rx="2" fill="url(#calendarGradient)" stroke="#667eea" stroke-width="1"/>
  
  <!-- Calendar Header -->
  <rect x="14" y="18" width="24" height="6" rx="2" fill="#667eea"/>
  
  <!-- Calendar Rings -->
  <circle cx="19" cy="16" r="1.5" fill="#667eea"/>
  <circle cx="33" cy="16" r="1.5" fill="#667eea"/>
  
  <!-- Calendar Grid Lines -->
  <line x1="18" y1="26" x2="34" y2="26" stroke="#667eea" stroke-width="0.5"/>
  <line x1="18" y1="30" x2="34" y2="30" stroke="#667eea" stroke-width="0.5"/>
  <line x1="18" y1="34" x2="34" y2="34" stroke="#667eea" stroke-width="0.5"/>
  
  <!-- Calendar Dots (dates) -->
  <circle cx="20" cy="28" r="1" fill="#667eea"/>
  <circle cx="24" cy="28" r="1" fill="#667eea"/>
  <circle cx="28" cy="28" r="1" fill="#667eea"/>
  <circle cx="32" cy="28" r="1" fill="#667eea"/>
  
  <circle cx="20" cy="32" r="1" fill="#667eea"/>
  <circle cx="24" cy="32" r="1" fill="#764ba2"/>
  <circle cx="28" cy="32" r="1" fill="#667eea"/>
  <circle cx="32" cy="32" r="1" fill="#667eea"/>
  
  <circle cx="20" cy="36" r="1" fill="#667eea"/>
  <circle cx="24" cy="36" r="1" fill="#667eea"/>
  <circle cx="28" cy="36" r="1" fill="#667eea"/>
  
  <!-- Certificate/Award Icon -->
  <g transform="translate(40, 40)">
    <!-- Certificate Background -->
    <rect x="0" y="0" width="16" height="12" rx="1" fill="url(#certificateGradient)" stroke="#d4af37" stroke-width="0.5"/>
    
    <!-- Certificate Lines -->
    <line x1="2" y1="3" x2="14" y2="3" stroke="#d4af37" stroke-width="0.5"/>
    <line x1="2" y1="5" x2="14" y2="5" stroke="#d4af37" stroke-width="0.5"/>
    <line x1="2" y1="7" x2="10" y2="7" stroke="#d4af37" stroke-width="0.5"/>
    
    <!-- Star/Award Symbol -->
    <polygon points="8,1 8.5,2.5 10,2.5 8.75,3.5 9.25,5 8,4 6.75,5 7.25,3.5 6,2.5 7.5,2.5" fill="#d4af37"/>
    
    <!-- Certificate Ribbon -->
    <rect x="6" y="12" width="4" height="6" fill="#d4af37"/>
    <polygon points="6,18 8,16 10,18" fill="#b8860b"/>
  </g>
  
  <!-- Connecting Line (showing relationship between calendar and certificate) -->
  <path d="M 38 32 Q 42 36 44 44" stroke="#ffffff" stroke-width="1.5" fill="none" stroke-dasharray="2,2" opacity="0.7"/>
</svg>
