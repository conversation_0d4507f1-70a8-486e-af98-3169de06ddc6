DEBUG - 2025-06-25 10:40:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 10:40:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-25 10:40:20 --> Error connecting to the database: mysqli_sql_exception: No connection could be made because the target machine actively refused it in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'event_managemen...', 3306, '', 0)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#5 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\BaseModel.php(717): CodeIgniter\Model->doFirst()
#6 C:\sertifikat\backup\myapp\app\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
#7 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#11 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 C:\sertifikat\backup\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\b...')
#14 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: No connection could be made because the target machine actively refused it in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#4 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\BaseModel.php(717): CodeIgniter\Model->doFirst()
#5 C:\sertifikat\backup\myapp\app\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
#6 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
#7 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 C:\sertifikat\backup\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\b...')
#13 {main}
CRITICAL - 2025-06-25 10:40:20 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: No connection could be made because the target machine actively refused it
[Method: POST, Route: auth/attemptLogin]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 3 SYSTEMPATH\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(717): CodeIgniter\Model->doFirst()
 5 APPPATH\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
ERROR - 2025-06-25 10:40:36 --> Error connecting to the database: mysqli_sql_exception: No connection could be made because the target machine actively refused it in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'event_managemen...', 3306, '', 0)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#5 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\BaseModel.php(717): CodeIgniter\Model->doFirst()
#6 C:\sertifikat\backup\myapp\app\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
#7 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#11 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 C:\sertifikat\backup\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\b...')
#14 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: No connection could be made because the target machine actively refused it in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#4 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\BaseModel.php(717): CodeIgniter\Model->doFirst()
#5 C:\sertifikat\backup\myapp\app\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
#6 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
#7 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 C:\sertifikat\backup\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\b...')
#13 {main}
CRITICAL - 2025-06-25 10:40:36 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: No connection could be made because the target machine actively refused it
[Method: POST, Route: auth/attemptLogin]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 3 SYSTEMPATH\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(717): CodeIgniter\Model->doFirst()
 5 APPPATH\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
ERROR - 2025-06-25 10:40:53 --> Error connecting to the database: ErrorException: mysqli::real_connect(): Error while reading greeting packet. PID=23792 in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'mysqli::real_co...', 'C:\\sertifikat\\b...', 201)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'event_managemen...', 3306, '', 0)
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#4 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#5 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#6 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\BaseModel.php(717): CodeIgniter\Model->doFirst()
#7 C:\sertifikat\backup\myapp\app\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 C:\sertifikat\backup\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\b...')
#15 {main}

Next mysqli_sql_exception: MySQL server has gone away in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'event_managemen...', 3306, '', 0)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#5 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\BaseModel.php(717): CodeIgniter\Model->doFirst()
#6 C:\sertifikat\backup\myapp\app\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
#7 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#11 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 C:\sertifikat\backup\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\b...')
#14 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: MySQL server has gone away in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#4 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\BaseModel.php(717): CodeIgniter\Model->doFirst()
#5 C:\sertifikat\backup\myapp\app\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
#6 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
#7 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 C:\sertifikat\backup\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\b...')
#13 {main}
CRITICAL - 2025-06-25 10:40:53 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: MySQL server has gone away
[Method: POST, Route: auth/attemptLogin]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 3 SYSTEMPATH\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(717): CodeIgniter\Model->doFirst()
 5 APPPATH\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
ERROR - 2025-06-25 10:41:17 --> Error connecting to the database: mysqli_sql_exception: No connection could be made because the target machine actively refused it in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'event_managemen...', 3306, '', 0)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#5 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\BaseModel.php(717): CodeIgniter\Model->doFirst()
#6 C:\sertifikat\backup\myapp\app\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
#7 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#11 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 C:\sertifikat\backup\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\b...')
#14 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: No connection could be made because the target machine actively refused it in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#4 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\BaseModel.php(717): CodeIgniter\Model->doFirst()
#5 C:\sertifikat\backup\myapp\app\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
#6 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
#7 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 C:\sertifikat\backup\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\b...')
#13 {main}
CRITICAL - 2025-06-25 10:41:17 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: No connection could be made because the target machine actively refused it
[Method: POST, Route: auth/attemptLogin]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 3 SYSTEMPATH\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(717): CodeIgniter\Model->doFirst()
 5 APPPATH\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-25 11:46:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 11:46:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-25 11:46:47 --> Error connecting to the database: mysqli_sql_exception: No connection could be made because the target machine actively refused it in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'event_managemen...', 3306, '', 0)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#5 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\BaseModel.php(717): CodeIgniter\Model->doFirst()
#6 C:\sertifikat\backup\myapp\app\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
#7 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#11 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 C:\sertifikat\backup\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\b...')
#14 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: No connection could be made because the target machine actively refused it in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#4 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\BaseModel.php(717): CodeIgniter\Model->doFirst()
#5 C:\sertifikat\backup\myapp\app\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
#6 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
#7 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 C:\sertifikat\backup\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\b...')
#13 {main}
CRITICAL - 2025-06-25 11:46:47 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: No connection could be made because the target machine actively refused it
[Method: POST, Route: auth/attemptLogin]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 3 SYSTEMPATH\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(717): CodeIgniter\Model->doFirst()
 5 APPPATH\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
ERROR - 2025-06-25 11:47:11 --> Error connecting to the database: ErrorException: mysqli::real_connect(): Error while reading greeting packet. PID=28248 in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'mysqli::real_co...', 'C:\\sertifikat\\b...', 201)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'event_managemen...', 3306, '', 0)
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#4 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#5 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#6 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\BaseModel.php(717): CodeIgniter\Model->doFirst()
#7 C:\sertifikat\backup\myapp\app\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 C:\sertifikat\backup\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\b...')
#15 {main}

Next mysqli_sql_exception: MySQL server has gone away in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'event_managemen...', 3306, '', 0)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#5 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\BaseModel.php(717): CodeIgniter\Model->doFirst()
#6 C:\sertifikat\backup\myapp\app\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
#7 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#11 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 C:\sertifikat\backup\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\b...')
#14 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: MySQL server has gone away in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#4 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\BaseModel.php(717): CodeIgniter\Model->doFirst()
#5 C:\sertifikat\backup\myapp\app\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
#6 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
#7 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 C:\sertifikat\backup\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\b...')
#13 {main}
CRITICAL - 2025-06-25 11:47:11 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: MySQL server has gone away
[Method: POST, Route: auth/attemptLogin]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 3 SYSTEMPATH\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(717): CodeIgniter\Model->doFirst()
 5 APPPATH\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
ERROR - 2025-06-25 11:47:22 --> Error connecting to the database: ErrorException: mysqli::real_connect(): Error while reading greeting packet. PID=28248 in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'mysqli::real_co...', 'C:\\sertifikat\\b...', 201)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'event_managemen...', 3306, '', 0)
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#4 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#5 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#6 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\BaseModel.php(717): CodeIgniter\Model->doFirst()
#7 C:\sertifikat\backup\myapp\app\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 C:\sertifikat\backup\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\b...')
#15 {main}

Next mysqli_sql_exception: MySQL server has gone away in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'event_managemen...', 3306, '', 0)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#5 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\BaseModel.php(717): CodeIgniter\Model->doFirst()
#6 C:\sertifikat\backup\myapp\app\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
#7 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#11 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 C:\sertifikat\backup\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\b...')
#14 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: MySQL server has gone away in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#4 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\BaseModel.php(717): CodeIgniter\Model->doFirst()
#5 C:\sertifikat\backup\myapp\app\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
#6 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
#7 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 C:\sertifikat\backup\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\b...')
#13 {main}
CRITICAL - 2025-06-25 11:47:22 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: MySQL server has gone away
[Method: POST, Route: auth/attemptLogin]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 3 SYSTEMPATH\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(717): CodeIgniter\Model->doFirst()
 5 APPPATH\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-25 11:47:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-25 11:47:54 --> Error connecting to the database: mysqli_sql_exception: No connection could be made because the target machine actively refused it in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'event_managemen...', 3306, '', 0)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#5 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\BaseModel.php(717): CodeIgniter\Model->doFirst()
#6 C:\sertifikat\backup\myapp\app\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
#7 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#11 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 C:\sertifikat\backup\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\b...')
#14 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: No connection could be made because the target machine actively refused it in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#4 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\BaseModel.php(717): CodeIgniter\Model->doFirst()
#5 C:\sertifikat\backup\myapp\app\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
#6 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
#7 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 C:\sertifikat\backup\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\b...')
#13 {main}
CRITICAL - 2025-06-25 11:47:54 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: No connection could be made because the target machine actively refused it
[Method: POST, Route: auth/attemptLogin]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 3 SYSTEMPATH\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(717): CodeIgniter\Model->doFirst()
 5 APPPATH\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
ERROR - 2025-06-25 11:50:03 --> Error connecting to the database: ErrorException: mysqli::real_connect(): Error while reading greeting packet. PID=28248 in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 [internal function]: CodeIgniter\Debug\Exceptions->errorHandler(2, 'mysqli::real_co...', 'C:\\sertifikat\\b...', 201)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'event_managemen...', 3306, '', 0)
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#4 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#5 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#6 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\BaseModel.php(717): CodeIgniter\Model->doFirst()
#7 C:\sertifikat\backup\myapp\app\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 C:\sertifikat\backup\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\b...')
#15 {main}

Next mysqli_sql_exception: MySQL server has gone away in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'event_managemen...', 3306, '', 0)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#5 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\BaseModel.php(717): CodeIgniter\Model->doFirst()
#6 C:\sertifikat\backup\myapp\app\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
#7 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#11 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 C:\sertifikat\backup\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\b...')
#14 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: MySQL server has gone away in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#4 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\BaseModel.php(717): CodeIgniter\Model->doFirst()
#5 C:\sertifikat\backup\myapp\app\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
#6 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
#7 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 C:\sertifikat\backup\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\b...')
#13 {main}
CRITICAL - 2025-06-25 11:50:03 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: MySQL server has gone away
[Method: POST, Route: auth/attemptLogin]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 3 SYSTEMPATH\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(717): CodeIgniter\Model->doFirst()
 5 APPPATH\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-25 11:50:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-25 11:50:16 --> Error connecting to the database: mysqli_sql_exception: No connection could be made because the target machine actively refused it in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'event_managemen...', 3306, '', 0)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#5 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\BaseModel.php(717): CodeIgniter\Model->doFirst()
#6 C:\sertifikat\backup\myapp\app\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
#7 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#11 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 C:\sertifikat\backup\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\b...')
#14 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: No connection could be made because the target machine actively refused it in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#4 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\BaseModel.php(717): CodeIgniter\Model->doFirst()
#5 C:\sertifikat\backup\myapp\app\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
#6 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
#7 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 C:\sertifikat\backup\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\b...')
#13 {main}
CRITICAL - 2025-06-25 11:50:16 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: No connection could be made because the target machine actively refused it
[Method: POST, Route: auth/attemptLogin]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 3 SYSTEMPATH\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(717): CodeIgniter\Model->doFirst()
 5 APPPATH\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
ERROR - 2025-06-25 11:57:27 --> Error connecting to the database: mysqli_sql_exception: No connection could be made because the target machine actively refused it in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'event_managemen...', 3306, '', 0)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#5 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\BaseModel.php(717): CodeIgniter\Model->doFirst()
#6 C:\sertifikat\backup\myapp\app\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
#7 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#11 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 C:\sertifikat\backup\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\b...')
#14 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: No connection could be made because the target machine actively refused it in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#4 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\BaseModel.php(717): CodeIgniter\Model->doFirst()
#5 C:\sertifikat\backup\myapp\app\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
#6 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
#7 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 C:\sertifikat\backup\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\b...')
#13 {main}
CRITICAL - 2025-06-25 11:57:27 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: No connection could be made because the target machine actively refused it
[Method: POST, Route: auth/attemptLogin]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 3 SYSTEMPATH\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(717): CodeIgniter\Model->doFirst()
 5 APPPATH\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-25 12:01:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:01:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:01:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:01:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:02:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:02:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:02:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:02:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:02:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:02:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:02:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:02:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:02:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:02:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:03:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:03:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:03:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:03:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:03:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:03:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:04:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:04:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:04:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:04:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:04:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:04:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-25 12:04:48 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "pages/pages/auth/create_admin.php"
[Method: GET, Route: auth/createAdmin]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('pages/pages/auth/create_admin.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/pages/auth/create_admin', [], true)
 3 APPPATH\Views\layout\main.php(281): view('pages/pages/auth/create_admin')
 4 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\backup\\myapp\\app\\Views\\layout\\main.php')
 5 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 6 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('layout/main', [], true)
 7 APPPATH\Controllers\Auth.php(128): view('layout/main', [...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->createAdmin()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-25 12:09:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-25 12:09:33 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "pages/pages/auth/create_admin.php"
[Method: GET, Route: auth/createAdmin]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('pages/pages/auth/create_admin.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/pages/auth/create_admin', [], true)
 3 APPPATH\Views\layout\main.php(281): view('pages/pages/auth/create_admin')
 4 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\backup\\myapp\\app\\Views\\layout\\main.php')
 5 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 6 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('layout/main', [], true)
 7 APPPATH\Controllers\Auth.php(128): view('layout/main', [...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->createAdmin()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-25 12:09:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:09:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-25 12:09:37 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "pages/pages/auth/create_admin.php"
[Method: GET, Route: auth/createAdmin]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('pages/pages/auth/create_admin.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/pages/auth/create_admin', [], true)
 3 APPPATH\Views\layout\main.php(281): view('pages/pages/auth/create_admin')
 4 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\backup\\myapp\\app\\Views\\layout\\main.php')
 5 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 6 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('layout/main', [], true)
 7 APPPATH\Controllers\Auth.php(128): view('layout/main', [...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->createAdmin()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-25 12:11:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-25 12:11:44 --> ErrorException: Allowed memory size of 536870912 bytes exhausted (tried to allocate 32768 bytes)
[Method: GET, Route: auth/createAdmin]
in APPPATH\Views\pages\auth\create_admin.php on line 1.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
DEBUG - 2025-06-25 12:11:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-25 12:12:12 --> ErrorException: Allowed memory size of 536870912 bytes exhausted (tried to allocate 32768 bytes)
[Method: GET, Route: auth/createAdmin]
in APPPATH\Views\pages\auth\create_admin.php on line 1.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
DEBUG - 2025-06-25 12:13:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:14:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:15:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:16:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:16:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:17:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:17:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:17:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:17:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:17:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:18:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:18:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:18:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:18:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:18:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:18:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:18:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:18:28 --> Current Time for Certificate Query: 2025-06-25 12:18:28
DEBUG - 2025-06-25 12:18:28 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-25 12:18:28 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-25 12:18:28 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-25 12:18:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:18:31 --> User ID: 9
DEBUG - 2025-06-25 12:18:31 --> Event ID: 17
INFO - 2025-06-25 12:19:58 --> CreateAdmin method called - Method: POST
DEBUG - 2025-06-25 12:19:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-25 12:19:58 --> Showing createAdmin form
INFO - 2025-06-25 12:20:16 --> CreateAdmin method called - Method: POST
DEBUG - 2025-06-25 12:20:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-25 12:20:16 --> Showing createAdmin form
INFO - 2025-06-25 12:21:30 --> CreateAdmin method called - Method: POST
DEBUG - 2025-06-25 12:21:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-25 12:21:30 --> Showing createAdmin form
INFO - 2025-06-25 12:21:54 --> CreateAdmin method called - Method: POST
DEBUG - 2025-06-25 12:21:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-25 12:21:54 --> Showing createAdmin form
INFO - 2025-06-25 12:24:11 --> CreateAdmin method called - Method: POST
DEBUG - 2025-06-25 12:24:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-25 12:24:11 --> Session check - isLoggedIn: true, role: admin
INFO - 2025-06-25 12:24:11 --> Request method check: POST (comparing with "post")
INFO - 2025-06-25 12:24:11 --> CreateAdmin POST request received
INFO - 2025-06-25 12:24:11 --> POST data: {"csrf_test_name":"998e97d7612c6368da66be515eed0318","name":"sfsfsfssfsfsf","email":"<EMAIL>","password":"alvin123","confirm_password":"alvin123","phone_number":"*****************"}
INFO - 2025-06-25 12:24:12 --> Validation passed for createAdmin
INFO - 2025-06-25 12:24:12 --> Admin account created successfully: <EMAIL>
DEBUG - 2025-06-25 12:24:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-25 12:24:20 --> CreateAdmin method called - Method: GET
DEBUG - 2025-06-25 12:24:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-25 12:24:20 --> Session check - isLoggedIn: true, role: admin
INFO - 2025-06-25 12:24:20 --> Request method check: GET (comparing with "post")
INFO - 2025-06-25 12:24:20 --> Showing createAdmin form
INFO - 2025-06-25 12:24:44 --> CreateAdmin method called - Method: POST
DEBUG - 2025-06-25 12:24:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-25 12:24:44 --> Session check - isLoggedIn: true, role: admin
INFO - 2025-06-25 12:24:44 --> Request method check: POST (comparing with "post")
INFO - 2025-06-25 12:24:44 --> CreateAdmin POST request received
INFO - 2025-06-25 12:24:44 --> POST data: {"csrf_test_name":"998e97d7612c6368da66be515eed0318","name":"marsha lenathea","email":"<EMAIL>","password":"alvin123","confirm_password":"alvin123","phone_number":"**********"}
INFO - 2025-06-25 12:24:44 --> Validation passed for createAdmin
INFO - 2025-06-25 12:24:44 --> Admin account created successfully: <EMAIL>
DEBUG - 2025-06-25 12:24:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:24:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:24:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:25:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:25:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:25:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:25:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:25:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:25:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:25:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:25:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:25:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:25:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:25:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:25:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:25:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:25:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:25:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:31:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:39:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:39:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:39:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:39:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:39:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:39:44 --> Current Time for Certificate Query: 2025-06-25 12:39:44
DEBUG - 2025-06-25 12:39:44 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-25 12:39:44 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-25 12:39:44 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-25 12:39:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:39:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:39:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:39:50 --> Current Time for Certificate Query: 2025-06-25 12:39:50
DEBUG - 2025-06-25 12:39:50 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-25 12:39:50 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-25 12:39:50 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-25 12:39:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:40:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:40:38 --> Current Time for Certificate Query: 2025-06-25 12:40:38
DEBUG - 2025-06-25 12:40:38 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-25 12:40:38 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-25 12:40:38 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-25 12:40:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:40:41 --> User ID: 9
DEBUG - 2025-06-25 12:40:41 --> Event ID: 17
DEBUG - 2025-06-25 12:41:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:41:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:41:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:41:17 --> Current Time for Certificate Query: 2025-06-25 12:41:17
DEBUG - 2025-06-25 12:41:17 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-25 12:41:17 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-25 12:41:17 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-25 12:43:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:43:33 --> User ID: 9
DEBUG - 2025-06-25 12:43:33 --> Event ID: 18
DEBUG - 2025-06-25 12:49:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:49:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:49:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:49:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:49:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:51:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-25 12:51:00 --> mysqli_sql_exception: Unknown column 'institution_name' in 'field list' in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `ev...', 0)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `ev...')
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `ev...')
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `ev...', Array, false)
#4 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\BaseModel.php(843): CodeIgniter\Model->doInsert(Array)
#6 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(800): CodeIgniter\BaseModel->insert(Array, true)
#7 C:\sertifikat\backup\myapp\app\Controllers\Events.php(138): CodeIgniter\Model->insert(Array)
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Events->store()
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 C:\sertifikat\backup\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\b...')
#15 {main}
CRITICAL - 2025-06-25 12:51:00 --> CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'institution_name' in 'field list'
[Method: POST, Route: events/store]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `institution_name`, `organizer_name`, `organizer_role`, `certificate_number`, `institution_logo`, `organizer_signature`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :institution_name:, :organizer_name:, :organizer_role:, :certificate_number:, :institution_logo:, :organizer_signature:, :created_at:, :updated_at:)', [...], false)
 2 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 3 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 4 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 5 APPPATH\Controllers\Events.php(138): CodeIgniter\Model->insert([...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
CRITICAL - 2025-06-25 12:51:00 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'institution_name' in 'field list'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `institution_name`, `organizer_name`, `organizer_role`, `certificate_number`, `institution_logo`, `organizer_signature`, `created_at`, `updated_at`) VALUES (\'event seminar bareng rizky ridho\', \'event seminar bareng rizky ridho\', \'2025-06-25T12:49\', \'2025-06-25T12:53\', \'Jakarta\', \'5\', \'Universitas Binance\', \'Ridho Fauzi\', \'Pembicara\', \'001/CERT/2024\', \'1750830660_cafdb5a35dca03c99275.jpg\', \'1750830660_5a830fc587bbf383d68d.png\', \'2025-06-25 12:51:00\', \'2025-06-25 12:51:00\')')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `institution_name`, `organizer_name`, `organizer_role`, `certificate_number`, `institution_logo`, `organizer_signature`, `created_at`, `updated_at`) VALUES (\'event seminar bareng rizky ridho\', \'event seminar bareng rizky ridho\', \'2025-06-25T12:49\', \'2025-06-25T12:53\', \'Jakarta\', \'5\', \'Universitas Binance\', \'Ridho Fauzi\', \'Pembicara\', \'001/CERT/2024\', \'1750830660_cafdb5a35dca03c99275.jpg\', \'1750830660_5a830fc587bbf383d68d.png\', \'2025-06-25 12:51:00\', \'2025-06-25 12:51:00\')')
 3 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `institution_name`, `organizer_name`, `organizer_role`, `certificate_number`, `institution_logo`, `organizer_signature`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :institution_name:, :organizer_name:, :organizer_role:, :certificate_number:, :institution_logo:, :organizer_signature:, :created_at:, :updated_at:)', [...], false)
 4 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 5 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 6 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 7 APPPATH\Controllers\Events.php(138): CodeIgniter\Model->insert([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
CRITICAL - 2025-06-25 12:51:00 --> [Caused by] mysqli_sql_exception: Unknown column 'institution_name' in 'field list'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `institution_name`, `organizer_name`, `organizer_role`, `certificate_number`, `institution_logo`, `organizer_signature`, `created_at`, `updated_at`) VALUES (\'event seminar bareng rizky ridho\', \'event seminar bareng rizky ridho\', \'2025-06-25T12:49\', \'2025-06-25T12:53\', \'Jakarta\', \'5\', \'Universitas Binance\', \'Ridho Fauzi\', \'Pembicara\', \'001/CERT/2024\', \'1750830660_cafdb5a35dca03c99275.jpg\', \'1750830660_5a830fc587bbf383d68d.png\', \'2025-06-25 12:51:00\', \'2025-06-25 12:51:00\')', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `institution_name`, `organizer_name`, `organizer_role`, `certificate_number`, `institution_logo`, `organizer_signature`, `created_at`, `updated_at`) VALUES (\'event seminar bareng rizky ridho\', \'event seminar bareng rizky ridho\', \'2025-06-25T12:49\', \'2025-06-25T12:53\', \'Jakarta\', \'5\', \'Universitas Binance\', \'Ridho Fauzi\', \'Pembicara\', \'001/CERT/2024\', \'1750830660_cafdb5a35dca03c99275.jpg\', \'1750830660_5a830fc587bbf383d68d.png\', \'2025-06-25 12:51:00\', \'2025-06-25 12:51:00\')')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `institution_name`, `organizer_name`, `organizer_role`, `certificate_number`, `institution_logo`, `organizer_signature`, `created_at`, `updated_at`) VALUES (\'event seminar bareng rizky ridho\', \'event seminar bareng rizky ridho\', \'2025-06-25T12:49\', \'2025-06-25T12:53\', \'Jakarta\', \'5\', \'Universitas Binance\', \'Ridho Fauzi\', \'Pembicara\', \'001/CERT/2024\', \'1750830660_cafdb5a35dca03c99275.jpg\', \'1750830660_5a830fc587bbf383d68d.png\', \'2025-06-25 12:51:00\', \'2025-06-25 12:51:00\')')
 4 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `institution_name`, `organizer_name`, `organizer_role`, `certificate_number`, `institution_logo`, `organizer_signature`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :institution_name:, :organizer_name:, :organizer_role:, :certificate_number:, :institution_logo:, :organizer_signature:, :created_at:, :updated_at:)', [...], false)
 5 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 6 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 7 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 8 APPPATH\Controllers\Events.php(138): CodeIgniter\Model->insert([...])
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
15 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
ERROR - 2025-06-25 12:53:13 --> mysqli_sql_exception: Table 'participants' already exists in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('CREATE TABLE `p...', 0)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('CREATE TABLE `p...')
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('CREATE TABLE `p...')
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\Forge.php(570): CodeIgniter\Database\BaseConnection->query('CREATE TABLE `p...')
#4 C:\sertifikat\backup\myapp\app\Database\Migrations\2025-06-01-081000_CreateParticipantsTable.php(53): CodeIgniter\Database\Forge->createTable('participants')
#5 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(876): App\Database\Migrations\CreateParticipantsTable->up()
#6 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(182): CodeIgniter\Database\MigrationRunner->migrate('up', Object(stdClass))
#7 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Commands\Database\Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest(NULL)
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CLI\Commands.php(74): CodeIgniter\Commands\Database\Migrate->run(Array)
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CLI\Console.php(47): CodeIgniter\CLI\Commands->run('migrate', Array)
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(360): CodeIgniter\CLI\Console->run()
#11 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#12 C:\sertifikat\backup\myapp\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#13 {main}
DEBUG - 2025-06-25 12:53:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-25 12:53:27 --> mysqli_sql_exception: Unknown column 'institution_name' in 'field list' in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `ev...', 0)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `ev...')
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `ev...')
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `ev...', Array, false)
#4 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\BaseModel.php(843): CodeIgniter\Model->doInsert(Array)
#6 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(800): CodeIgniter\BaseModel->insert(Array, true)
#7 C:\sertifikat\backup\myapp\app\Controllers\Events.php(138): CodeIgniter\Model->insert(Array)
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Events->store()
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 C:\sertifikat\backup\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\b...')
#15 {main}
CRITICAL - 2025-06-25 12:53:27 --> CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'institution_name' in 'field list'
[Method: POST, Route: events/store]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `institution_name`, `organizer_name`, `organizer_role`, `certificate_number`, `institution_logo`, `organizer_signature`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :institution_name:, :organizer_name:, :organizer_role:, :certificate_number:, :institution_logo:, :organizer_signature:, :created_at:, :updated_at:)', [...], false)
 2 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 3 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 4 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 5 APPPATH\Controllers\Events.php(138): CodeIgniter\Model->insert([...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
CRITICAL - 2025-06-25 12:53:27 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'institution_name' in 'field list'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `institution_name`, `organizer_name`, `organizer_role`, `certificate_number`, `institution_logo`, `organizer_signature`, `created_at`, `updated_at`) VALUES (\'event seminar bareng rizky ridho\', \'event seminar bareng rizky ridho\', \'2025-06-25T12:49\', \'2025-06-25T12:53\', \'Jakarta\', \'5\', \'Universitas Binance\', \'Ridho Fauzi\', \'Pembicara\', \'001/CERT/2024\', \'1750830807_ea5d4d31cc526b643509.jpg\', \'1750830807_6e7d9c7dd260fe9e9652.png\', \'2025-06-25 12:53:27\', \'2025-06-25 12:53:27\')')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `institution_name`, `organizer_name`, `organizer_role`, `certificate_number`, `institution_logo`, `organizer_signature`, `created_at`, `updated_at`) VALUES (\'event seminar bareng rizky ridho\', \'event seminar bareng rizky ridho\', \'2025-06-25T12:49\', \'2025-06-25T12:53\', \'Jakarta\', \'5\', \'Universitas Binance\', \'Ridho Fauzi\', \'Pembicara\', \'001/CERT/2024\', \'1750830807_ea5d4d31cc526b643509.jpg\', \'1750830807_6e7d9c7dd260fe9e9652.png\', \'2025-06-25 12:53:27\', \'2025-06-25 12:53:27\')')
 3 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `institution_name`, `organizer_name`, `organizer_role`, `certificate_number`, `institution_logo`, `organizer_signature`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :institution_name:, :organizer_name:, :organizer_role:, :certificate_number:, :institution_logo:, :organizer_signature:, :created_at:, :updated_at:)', [...], false)
 4 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 5 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 6 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 7 APPPATH\Controllers\Events.php(138): CodeIgniter\Model->insert([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
CRITICAL - 2025-06-25 12:53:27 --> [Caused by] mysqli_sql_exception: Unknown column 'institution_name' in 'field list'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `institution_name`, `organizer_name`, `organizer_role`, `certificate_number`, `institution_logo`, `organizer_signature`, `created_at`, `updated_at`) VALUES (\'event seminar bareng rizky ridho\', \'event seminar bareng rizky ridho\', \'2025-06-25T12:49\', \'2025-06-25T12:53\', \'Jakarta\', \'5\', \'Universitas Binance\', \'Ridho Fauzi\', \'Pembicara\', \'001/CERT/2024\', \'1750830807_ea5d4d31cc526b643509.jpg\', \'1750830807_6e7d9c7dd260fe9e9652.png\', \'2025-06-25 12:53:27\', \'2025-06-25 12:53:27\')', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `institution_name`, `organizer_name`, `organizer_role`, `certificate_number`, `institution_logo`, `organizer_signature`, `created_at`, `updated_at`) VALUES (\'event seminar bareng rizky ridho\', \'event seminar bareng rizky ridho\', \'2025-06-25T12:49\', \'2025-06-25T12:53\', \'Jakarta\', \'5\', \'Universitas Binance\', \'Ridho Fauzi\', \'Pembicara\', \'001/CERT/2024\', \'1750830807_ea5d4d31cc526b643509.jpg\', \'1750830807_6e7d9c7dd260fe9e9652.png\', \'2025-06-25 12:53:27\', \'2025-06-25 12:53:27\')')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `institution_name`, `organizer_name`, `organizer_role`, `certificate_number`, `institution_logo`, `organizer_signature`, `created_at`, `updated_at`) VALUES (\'event seminar bareng rizky ridho\', \'event seminar bareng rizky ridho\', \'2025-06-25T12:49\', \'2025-06-25T12:53\', \'Jakarta\', \'5\', \'Universitas Binance\', \'Ridho Fauzi\', \'Pembicara\', \'001/CERT/2024\', \'1750830807_ea5d4d31cc526b643509.jpg\', \'1750830807_6e7d9c7dd260fe9e9652.png\', \'2025-06-25 12:53:27\', \'2025-06-25 12:53:27\')')
 4 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `institution_name`, `organizer_name`, `organizer_role`, `certificate_number`, `institution_logo`, `organizer_signature`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :institution_name:, :organizer_name:, :organizer_role:, :certificate_number:, :institution_logo:, :organizer_signature:, :created_at:, :updated_at:)', [...], false)
 5 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 6 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 7 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 8 APPPATH\Controllers\Events.php(138): CodeIgniter\Model->insert([...])
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
15 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-25 12:53:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:58:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:59:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:59:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:59:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:59:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:59:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:59:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:59:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-25 12:59:47 --> Berhasil Daftar: <EMAIL> untuk event 21
DEBUG - 2025-06-25 12:59:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:59:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:59:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:59:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:59:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:59:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 12:59:55 --> Current Time for Certificate Query: 2025-06-25 12:59:55
DEBUG - 2025-06-25 12:59:55 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-25 12:59:55 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-25 12:59:55 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-25 12:59:55 --> Event: dgdgdgafafa, EndDate: 2025-06-25 13:00:00, IsCompleted: false
DEBUG - 2025-06-25 13:01:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:01:16 --> Current Time for Certificate Query: 2025-06-25 13:01:16
DEBUG - 2025-06-25 13:01:16 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-25 13:01:16 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-25 13:01:16 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-25 13:01:16 --> Event: dgdgdgafafa, EndDate: 2025-06-25 13:00:00, IsCompleted: true
DEBUG - 2025-06-25 13:01:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:01:18 --> User ID: 9
DEBUG - 2025-06-25 13:01:18 --> Event ID: 21
DEBUG - 2025-06-25 13:01:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:01:22 --> Current Time for Certificate Query: 2025-06-25 13:01:22
DEBUG - 2025-06-25 13:01:22 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-25 13:01:22 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-25 13:01:22 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-25 13:01:22 --> Event: dgdgdgafafa, EndDate: 2025-06-25 13:00:00, IsCompleted: true
DEBUG - 2025-06-25 13:01:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:01:23 --> User ID: 9
DEBUG - 2025-06-25 13:01:23 --> Event ID: 21
DEBUG - 2025-06-25 13:01:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:05:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:05:38 --> User ID: 9
DEBUG - 2025-06-25 13:05:38 --> Event ID: 21
DEBUG - 2025-06-25 13:05:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:05:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:05:44 --> Current Time for Certificate Query: 2025-06-25 13:05:44
DEBUG - 2025-06-25 13:05:44 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-25 13:05:44 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-25 13:05:44 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-25 13:05:44 --> Event: dgdgdgafafa, EndDate: 2025-06-25 13:00:00, IsCompleted: true
DEBUG - 2025-06-25 13:05:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:05:46 --> User ID: 9
DEBUG - 2025-06-25 13:05:46 --> Event ID: 18
DEBUG - 2025-06-25 13:05:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:05:50 --> Current Time for Certificate Query: 2025-06-25 13:05:50
DEBUG - 2025-06-25 13:05:50 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-25 13:05:50 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-25 13:05:50 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-25 13:05:50 --> Event: dgdgdgafafa, EndDate: 2025-06-25 13:00:00, IsCompleted: true
DEBUG - 2025-06-25 13:05:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:05:52 --> User ID: 9
DEBUG - 2025-06-25 13:05:52 --> Event ID: 17
DEBUG - 2025-06-25 13:05:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:09:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:09:49 --> User ID: 9
DEBUG - 2025-06-25 13:09:49 --> Event ID: 17
DEBUG - 2025-06-25 13:10:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:10:03 --> Current Time for Certificate Query: 2025-06-25 13:10:03
DEBUG - 2025-06-25 13:10:03 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-25 13:10:03 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-25 13:10:03 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-25 13:10:03 --> Event: dgdgdgafafa, EndDate: 2025-06-25 13:00:00, IsCompleted: true
DEBUG - 2025-06-25 13:10:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:10:05 --> User ID: 9
DEBUG - 2025-06-25 13:10:05 --> Event ID: 17
DEBUG - 2025-06-25 13:10:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:10:07 --> Current Time for Certificate Query: 2025-06-25 13:10:07
DEBUG - 2025-06-25 13:10:07 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-25 13:10:07 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-25 13:10:07 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-25 13:10:07 --> Event: dgdgdgafafa, EndDate: 2025-06-25 13:00:00, IsCompleted: true
DEBUG - 2025-06-25 13:10:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:10:09 --> User ID: 9
DEBUG - 2025-06-25 13:10:09 --> Event ID: 21
DEBUG - 2025-06-25 13:10:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:13:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:13:43 --> User ID: 9
DEBUG - 2025-06-25 13:13:43 --> Event ID: 21
DEBUG - 2025-06-25 13:13:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:14:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:14:58 --> User ID: 9
DEBUG - 2025-06-25 13:14:58 --> Event ID: 21
DEBUG - 2025-06-25 13:15:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:15:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:17:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:17:29 --> User ID: 9
DEBUG - 2025-06-25 13:17:29 --> Event ID: 21
DEBUG - 2025-06-25 13:17:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:18:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:18:28 --> User ID: 9
DEBUG - 2025-06-25 13:18:28 --> Event ID: 21
DEBUG - 2025-06-25 13:18:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:18:31 --> User ID: 9
DEBUG - 2025-06-25 13:18:31 --> Event ID: 21
DEBUG - 2025-06-25 13:18:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:21:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:21:27 --> User ID: 9
DEBUG - 2025-06-25 13:21:27 --> Event ID: 21
DEBUG - 2025-06-25 13:21:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:23:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:23:29 --> User ID: 9
DEBUG - 2025-06-25 13:23:29 --> Event ID: 21
DEBUG - 2025-06-25 13:23:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:25:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:25:01 --> User ID: 9
DEBUG - 2025-06-25 13:25:01 --> Event ID: 21
DEBUG - 2025-06-25 13:25:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:25:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:25:45 --> User ID: 9
DEBUG - 2025-06-25 13:25:45 --> Event ID: 21
DEBUG - 2025-06-25 13:25:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:29:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:29:22 --> User ID: 9
DEBUG - 2025-06-25 13:29:22 --> Event ID: 21
DEBUG - 2025-06-25 13:29:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:31:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:31:05 --> User ID: 9
DEBUG - 2025-06-25 13:31:05 --> Event ID: 21
DEBUG - 2025-06-25 13:31:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:33:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:33:28 --> User ID: 9
DEBUG - 2025-06-25 13:33:28 --> Event ID: 21
DEBUG - 2025-06-25 13:33:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:34:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 13:34:58 --> User ID: 9
DEBUG - 2025-06-25 13:34:58 --> Event ID: 21
DEBUG - 2025-06-25 15:45:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 15:45:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 15:46:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 15:46:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 15:46:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 15:46:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 15:46:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 15:46:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 15:46:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 15:46:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 15:46:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 15:46:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 15:46:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 15:47:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 15:47:04 --> Current Time for Certificate Query: 2025-06-25 15:47:04
DEBUG - 2025-06-25 15:47:04 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-25 15:47:04 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-25 15:47:04 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-25 15:47:04 --> Event: dgdgdgafafa, EndDate: 2025-06-25 13:00:00, IsCompleted: true
DEBUG - 2025-06-25 15:47:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 15:47:07 --> User ID: 9
DEBUG - 2025-06-25 15:47:07 --> Event ID: 21
DEBUG - 2025-06-25 15:52:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 15:52:22 --> User ID: 9
DEBUG - 2025-06-25 15:52:22 --> Event ID: 21
DEBUG - 2025-06-25 15:59:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 15:59:01 --> User ID: 9
DEBUG - 2025-06-25 15:59:01 --> Event ID: 21
DEBUG - 2025-06-25 15:59:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 15:59:08 --> Current Time for Certificate Query: 2025-06-25 15:59:08
DEBUG - 2025-06-25 15:59:08 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-25 15:59:08 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-25 15:59:08 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-25 15:59:08 --> Event: dgdgdgafafa, EndDate: 2025-06-25 13:00:00, IsCompleted: true
DEBUG - 2025-06-25 15:59:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 16:02:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 16:02:59 --> Current Time for Certificate Query: 2025-06-25 16:02:59
DEBUG - 2025-06-25 16:02:59 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-25 16:02:59 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-25 16:02:59 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-25 16:02:59 --> Event: dgdgdgafafa, EndDate: 2025-06-25 13:00:00, IsCompleted: true
DEBUG - 2025-06-25 16:10:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 16:10:47 --> Current Time for Certificate Query: 2025-06-25 16:10:47
DEBUG - 2025-06-25 16:10:47 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-25 16:10:47 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-25 16:10:47 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-25 16:10:47 --> Event: dgdgdgafafa, EndDate: 2025-06-25 13:00:00, IsCompleted: true
DEBUG - 2025-06-25 16:10:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 16:10:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 16:10:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 16:10:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 16:10:59 --> Current Time for Certificate Query: 2025-06-25 16:10:59
DEBUG - 2025-06-25 16:10:59 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-25 16:10:59 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-25 16:10:59 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-25 16:10:59 --> Event: dgdgdgafafa, EndDate: 2025-06-25 13:00:00, IsCompleted: true
DEBUG - 2025-06-25 16:11:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 16:11:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 16:14:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 16:14:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 16:14:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 16:14:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 16:14:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 16:17:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 16:17:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 16:17:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 16:18:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 16:18:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 16:20:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 16:20:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 16:20:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 16:37:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 16:37:30 --> Current Time for Certificate Query: 2025-06-25 16:37:30
DEBUG - 2025-06-25 16:37:30 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-25 16:37:30 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-25 16:37:30 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-25 16:37:30 --> Event: dgdgdgafafa, EndDate: 2025-06-25 13:00:00, IsCompleted: true
DEBUG - 2025-06-25 16:37:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 16:39:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 16:39:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 16:40:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 16:40:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 16:57:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 16:57:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 16:57:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-25 16:57:34 --> ErrorException: Undefined variable $selected_event
[Method: GET, Route: reports/participants]
in APPPATH\Views\pages\reports\participants.php on line 23.
 1 APPPATH\Views\pages\reports\participants.php(23): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined variable $selected_event', 'C:\\sertifikat\\backup\\myapp\\app\\Views\\pages\\reports\\participants.php', 23)
 2 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\backup\\myapp\\app\\Views\\pages\\reports\\participants.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/reports/participants', [], true)
 5 APPPATH\Views\layout\main.php(281): view('pages/reports/participants')
 6 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\backup\\myapp\\app\\Views\\layout\\main.php')
 7 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 8 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('layout/main', [], true)
 9 APPPATH\Controllers\Reports.php(77): view('layout/main', [...])
10 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Reports->participants()
11 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Reports))
12 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
13 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
15 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
16 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-25 16:57:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 16:59:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-25 16:59:24 --> ErrorException: Undefined variable $selected_event
[Method: GET, Route: reports/participants]
in APPPATH\Views\pages\reports\participants.php on line 23.
 1 APPPATH\Views\pages\reports\participants.php(23): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined variable $selected_event', 'C:\\sertifikat\\backup\\myapp\\app\\Views\\pages\\reports\\participants.php', 23)
 2 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\backup\\myapp\\app\\Views\\pages\\reports\\participants.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/reports/participants', [], true)
 5 APPPATH\Views\layout\main.php(281): view('pages/reports/participants')
 6 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\backup\\myapp\\app\\Views\\layout\\main.php')
 7 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 8 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('layout/main', [], true)
 9 APPPATH\Controllers\Reports.php(77): view('layout/main', [...])
10 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Reports->participants()
11 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Reports))
12 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
13 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
15 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
16 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-25 16:59:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:02:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-25 17:02:11 --> CodeIgniter\Exceptions\RuntimeException: View themes, no current section.
[Method: GET, Route: reports/events]
in SYSTEMPATH\View\View.php on line 430.
 1 APPPATH\Views\pages\reports\events.php(335): CodeIgniter\View\View->endSection()
 2 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\backup\\myapp\\app\\Views\\pages\\reports\\events.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/reports/events', [], true)
 5 APPPATH\Views\layout\main.php(281): view('pages/reports/events')
 6 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\backup\\myapp\\app\\Views\\layout\\main.php')
 7 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 8 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('layout/main', [], true)
 9 APPPATH\Controllers\Reports.php(58): view('layout/main', [...])
10 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Reports->events()
11 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Reports))
12 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
13 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
15 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
16 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-25 17:05:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:05:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:05:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:10:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:10:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:10:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:10:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:10:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:10:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:10:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:10:33 --> Current Time for Certificate Query: 2025-06-25 17:10:33
DEBUG - 2025-06-25 17:10:33 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-25 17:10:33 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-25 17:10:33 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-25 17:10:33 --> Event: dgdgdgafafa, EndDate: 2025-06-25 13:00:00, IsCompleted: true
DEBUG - 2025-06-25 17:10:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:11:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:11:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:28:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:29:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:29:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:29:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:29:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:29:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:29:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:29:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:29:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:29:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:32:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:32:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:33:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:33:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:33:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:34:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:34:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:35:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:38:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:38:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-25 17:38:03 --> CreateAdmin method called - Method: GET
DEBUG - 2025-06-25 17:38:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-25 17:38:03 --> Session check - isLoggedIn: true, role: admin
INFO - 2025-06-25 17:38:03 --> Request method check: GET (comparing with "post")
INFO - 2025-06-25 17:38:03 --> Showing createAdmin form
DEBUG - 2025-06-25 17:38:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:38:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:40:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:40:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:40:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:41:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:41:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:42:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:43:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:43:35 --> Current Time for Certificate Query: 2025-06-25 17:43:35
DEBUG - 2025-06-25 17:43:35 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-25 17:43:35 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-25 17:43:35 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-25 17:43:35 --> Event: dgdgdgafafa, EndDate: 2025-06-25 13:00:00, IsCompleted: true
DEBUG - 2025-06-25 17:43:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:43:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:43:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:43:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:43:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:44:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:44:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:44:08 --> Current Time for Certificate Query: 2025-06-25 17:44:08
DEBUG - 2025-06-25 17:44:08 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-25 17:44:08 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-25 17:44:08 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-25 17:44:08 --> Event: dgdgdgafafa, EndDate: 2025-06-25 13:00:00, IsCompleted: true
DEBUG - 2025-06-25 17:44:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:44:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:46:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:46:55 --> Current Time for Certificate Query: 2025-06-25 17:46:55
DEBUG - 2025-06-25 17:46:55 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-25 17:46:55 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-25 17:46:55 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-25 17:46:55 --> Event: dgdgdgafafa, EndDate: 2025-06-25 13:00:00, IsCompleted: true
DEBUG - 2025-06-25 17:46:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-25 17:46:57 --> Error: Call to undefined method App\Controllers\Events::addGradientBackground()
[Method: GET, Route: events/download-certificate/17]
in APPPATH\Controllers\Events.php on line 447.
 1 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->downloadCertificate(17)
 2 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-25 17:46:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:46:59 --> Current Time for Certificate Query: 2025-06-25 17:46:59
DEBUG - 2025-06-25 17:46:59 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-25 17:46:59 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-25 17:46:59 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-25 17:46:59 --> Event: dgdgdgafafa, EndDate: 2025-06-25 13:00:00, IsCompleted: true
DEBUG - 2025-06-25 17:48:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:48:37 --> Current Time for Certificate Query: 2025-06-25 17:48:37
DEBUG - 2025-06-25 17:48:37 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-25 17:48:37 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-25 17:48:37 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-25 17:48:37 --> Event: dgdgdgafafa, EndDate: 2025-06-25 13:00:00, IsCompleted: true
DEBUG - 2025-06-25 17:48:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:50:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:50:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-25 17:50:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-25 17:50:30 --> TypeError: App\Controllers\Reports::index(): Return value must be of type string, CodeIgniter\HTTP\RedirectResponse returned
[Method: GET, Route: reports]
in APPPATH\Controllers\Reports.php on line 26.
 1 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Reports->index()
 2 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Reports))
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-25 17:50:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-25 17:50:41 --> TypeError: App\Controllers\Reports::index(): Return value must be of type string, CodeIgniter\HTTP\RedirectResponse returned
[Method: GET, Route: reports]
in APPPATH\Controllers\Reports.php on line 26.
 1 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Reports->index()
 2 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Reports))
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-25 17:52:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-25 17:52:14 --> TypeError: App\Controllers\Reports::index(): Return value must be of type string, CodeIgniter\HTTP\RedirectResponse returned
[Method: GET, Route: reports]
in APPPATH\Controllers\Reports.php on line 26.
 1 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Reports->index()
 2 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Reports))
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
