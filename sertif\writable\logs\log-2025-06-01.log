ERROR - 2025-06-01 00:53:27 --> mysqli_sql_exception: Table 'participants' already exists in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('CREATE TABLE `p...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('CREATE TABLE `p...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('CREATE TABLE `p...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\Forge.php(570): CodeIgniter\Database\BaseConnection->query('CREATE TABLE `p...')
#4 C:\sertifikat\myapp\app\Database\Migrations\2025-06-01-081000_CreateParticipantsTable.php(53): CodeIgniter\Database\Forge->createTable('participants')
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(876): App\Database\Migrations\CreateParticipantsTable->up()
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(182): CodeIgniter\Database\MigrationRunner->migrate('up', Object(stdClass))
#7 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Commands\Database\Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest(NULL)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Commands.php(74): CodeIgniter\Commands\Database\Migrate->run(Array)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Console.php(47): CodeIgniter\CLI\Commands->run('migrate', Array)
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(360): CodeIgniter\CLI\Console->run()
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#12 C:\sertifikat\myapp\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#13 {main}
DEBUG - 2025-06-01 00:54:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 00:55:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 00:55:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 00:55:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:22:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:22:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:22:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:22:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:22:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:23:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:23:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:23:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:23:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:23:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:23:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:23:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:23:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:23:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:23:25 --> SQL Query: 
DEBUG - 2025-06-01 02:23:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:23:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:24:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:27:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:27:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:27:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:27:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:27:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:29:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:29:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:29:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:30:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:30:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:31:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:32:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:32:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:33:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:33:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:34:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:34:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:39:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:39:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:39:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:40:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:40:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:40:23 --> User ID: 5
DEBUG - 2025-06-01 02:40:23 --> Event ID: 6
DEBUG - 2025-06-01 02:40:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:40:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:40:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:40:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:40:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:40:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-01 02:42:24 --> mysqli_sql_exception: Table 'participants' already exists in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('CREATE TABLE `p...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('CREATE TABLE `p...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('CREATE TABLE `p...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\Forge.php(570): CodeIgniter\Database\BaseConnection->query('CREATE TABLE `p...')
#4 C:\sertifikat\myapp\app\Database\Migrations\2025-06-01-081000_CreateParticipantsTable.php(53): CodeIgniter\Database\Forge->createTable('participants')
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(876): App\Database\Migrations\CreateParticipantsTable->up()
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(182): CodeIgniter\Database\MigrationRunner->migrate('up', Object(stdClass))
#7 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Commands\Database\Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest(NULL)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Commands.php(74): CodeIgniter\Commands\Database\Migrate->run(Array)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Console.php(47): CodeIgniter\CLI\Commands->run('migrate', Array)
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(360): CodeIgniter\CLI\Console->run()
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#12 C:\sertifikat\myapp\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#13 {main}
DEBUG - 2025-06-01 02:42:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:42:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:42:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:42:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:42:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:42:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:42:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:42:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:49:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:49:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:49:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:49:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:55:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:55:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:55:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:55:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:55:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:56:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-01 02:56:45 --> mysqli_sql_exception: Table 'participants' already exists in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('CREATE TABLE `p...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('CREATE TABLE `p...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('CREATE TABLE `p...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\Forge.php(570): CodeIgniter\Database\BaseConnection->query('CREATE TABLE `p...')
#4 C:\sertifikat\myapp\app\Database\Migrations\2025-06-01-081000_CreateParticipantsTable.php(53): CodeIgniter\Database\Forge->createTable('participants')
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(876): App\Database\Migrations\CreateParticipantsTable->up()
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(182): CodeIgniter\Database\MigrationRunner->migrate('up', Object(stdClass))
#7 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Commands\Database\Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest(NULL)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Commands.php(74): CodeIgniter\Commands\Database\Migrate->run(Array)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Console.php(47): CodeIgniter\CLI\Commands->run('migrate', Array)
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(360): CodeIgniter\CLI\Console->run()
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#12 C:\sertifikat\myapp\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#13 {main}
DEBUG - 2025-06-01 02:57:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:57:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:57:29 --> SQL Query: 
DEBUG - 2025-06-01 02:57:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:57:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:57:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:57:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:57:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:57:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:57:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:57:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:58:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:58:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:58:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:58:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:58:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:58:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:58:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:58:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:58:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:58:24 --> User ID: 5
DEBUG - 2025-06-01 02:58:24 --> Event ID: 7
DEBUG - 2025-06-01 02:58:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:59:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:59:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:59:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 02:59:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 03:00:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-01 03:06:31 --> mysqli_sql_exception: Table 'participants' already exists in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('CREATE TABLE `p...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('CREATE TABLE `p...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('CREATE TABLE `p...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\Forge.php(570): CodeIgniter\Database\BaseConnection->query('CREATE TABLE `p...')
#4 C:\sertifikat\myapp\app\Database\Migrations\2025-06-01-081000_CreateParticipantsTable.php(53): CodeIgniter\Database\Forge->createTable('participants')
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(876): App\Database\Migrations\CreateParticipantsTable->up()
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(182): CodeIgniter\Database\MigrationRunner->migrate('up', Object(stdClass))
#7 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Commands\Database\Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest(NULL)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Commands.php(74): CodeIgniter\Commands\Database\Migrate->run(Array)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Console.php(47): CodeIgniter\CLI\Commands->run('migrate', Array)
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(360): CodeIgniter\CLI\Console->run()
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#12 C:\sertifikat\myapp\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#13 {main}
DEBUG - 2025-06-01 03:08:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 03:08:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 03:08:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 03:08:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 03:08:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 03:08:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 03:08:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 03:08:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 03:08:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 03:08:56 --> SQL Query: 
ERROR - 2025-06-01 03:13:37 --> mysqli_sql_exception: Duplicate entry '2' for key 'PRIMARY' in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `us...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `us...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `us...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `us...', Array, false)
#4 C:\sertifikat\myapp\app\Database\Seeds\SpecificUserAndEventSeeder.php(25): CodeIgniter\Database\BaseBuilder->insert(Array)
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\Seeder.php(149): App\Database\Seeds\SpecificUserAndEventSeeder->run()
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Commands\Database\Seed.php(79): CodeIgniter\Database\Seeder->call('App\\Database\\Se...')
#7 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Commands.php(74): CodeIgniter\Commands\Database\Seed->run(Array)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Console.php(47): CodeIgniter\CLI\Commands->run('db:seed', Array)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(360): CodeIgniter\CLI\Console->run()
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#11 C:\sertifikat\myapp\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#12 {main}
DEBUG - 2025-06-01 03:14:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 03:14:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 03:15:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 03:15:52 --> SQL Query: 
DEBUG - 2025-06-01 03:15:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 03:15:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 03:15:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 03:15:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 03:15:59 --> SQL Query: 
DEBUG - 2025-06-01 04:02:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 04:02:03 --> SQL Query: 
DEBUG - 2025-06-01 04:02:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 04:02:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-01 04:02:05 --> mysqli_sql_exception: Not unique table/alias: 'events' in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT COUNT(*)...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT COUNT(*)...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT COUNT(*)...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1733): CodeIgniter\Database\BaseConnection->query('SELECT COUNT(*)...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(676): CodeIgniter\Database\BaseBuilder->countAllResults(false)
#5 C:\sertifikat\myapp\app\Controllers\Dashboard.php(54): CodeIgniter\Model->countAllResults(false)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Dashboard->index()
#7 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Dashboard))
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#13 {main}
CRITICAL - 2025-06-01 04:02:05 --> CodeIgniter\Database\Exceptions\DatabaseException: Not unique table/alias: 'events'
[Method: GET, Route: dashboard]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1733): CodeIgniter\Database\BaseConnection->query('SELECT COUNT(*) AS `numrows`
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `user_id` = :user_id:
AND `participants`.`event_id` IS NOT NULL
AND `participants`.`user_id` = :participants.user_id:
AND `events`.`start_date` > :events.start_date:', [...], false)
 2 SYSTEMPATH\Model.php(676): CodeIgniter\Database\BaseBuilder->countAllResults(false)
 3 APPPATH\Controllers\Dashboard.php(54): CodeIgniter\Model->countAllResults(false)
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Dashboard->index()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Dashboard))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
10 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-06-01 04:02:05 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Not unique table/alias: 'events'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT COUNT(*) AS `numrows`
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `user_id` = \'5\'
AND `participants`.`event_id` IS NOT NULL
AND `participants`.`user_id` = \'5\'
AND `events`.`start_date` > \'2025-06-01 04:02:05\'')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT COUNT(*) AS `numrows`
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `user_id` = \'5\'
AND `participants`.`event_id` IS NOT NULL
AND `participants`.`user_id` = \'5\'
AND `events`.`start_date` > \'2025-06-01 04:02:05\'')
 3 SYSTEMPATH\Database\BaseBuilder.php(1733): CodeIgniter\Database\BaseConnection->query('SELECT COUNT(*) AS `numrows`
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `user_id` = :user_id:
AND `participants`.`event_id` IS NOT NULL
AND `participants`.`user_id` = :participants.user_id:
AND `events`.`start_date` > :events.start_date:', [...], false)
 4 SYSTEMPATH\Model.php(676): CodeIgniter\Database\BaseBuilder->countAllResults(false)
 5 APPPATH\Controllers\Dashboard.php(54): CodeIgniter\Model->countAllResults(false)
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Dashboard->index()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Dashboard))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-06-01 04:02:05 --> [Caused by] mysqli_sql_exception: Not unique table/alias: 'events'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT COUNT(*) AS `numrows`
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `user_id` = \'5\'
AND `participants`.`event_id` IS NOT NULL
AND `participants`.`user_id` = \'5\'
AND `events`.`start_date` > \'2025-06-01 04:02:05\'', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT COUNT(*) AS `numrows`
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `user_id` = \'5\'
AND `participants`.`event_id` IS NOT NULL
AND `participants`.`user_id` = \'5\'
AND `events`.`start_date` > \'2025-06-01 04:02:05\'')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT COUNT(*) AS `numrows`
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `user_id` = \'5\'
AND `participants`.`event_id` IS NOT NULL
AND `participants`.`user_id` = \'5\'
AND `events`.`start_date` > \'2025-06-01 04:02:05\'')
 4 SYSTEMPATH\Database\BaseBuilder.php(1733): CodeIgniter\Database\BaseConnection->query('SELECT COUNT(*) AS `numrows`
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `user_id` = :user_id:
AND `participants`.`event_id` IS NOT NULL
AND `participants`.`user_id` = :participants.user_id:
AND `events`.`start_date` > :events.start_date:', [...], false)
 5 SYSTEMPATH\Model.php(676): CodeIgniter\Database\BaseBuilder->countAllResults(false)
 6 APPPATH\Controllers\Dashboard.php(54): CodeIgniter\Model->countAllResults(false)
 7 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Dashboard->index()
 8 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Dashboard))
 9 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
11 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
12 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
13 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-06-01 04:03:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 04:03:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 04:03:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 04:03:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 04:03:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 04:03:14 --> SQL Query: 
DEBUG - 2025-06-01 04:23:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 04:23:27 --> SQL Query: 
DEBUG - 2025-06-01 04:23:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 04:23:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 04:23:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 04:23:31 --> SQL Query: 
DEBUG - 2025-06-01 13:40:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:40:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:40:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:40:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:40:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:41:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:41:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:43:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:43:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:43:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:44:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:44:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:45:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:46:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:46:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:46:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:46:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:48:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:48:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:48:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:48:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:48:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:49:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:49:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:49:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:49:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:49:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:49:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:49:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:50:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:50:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:50:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:50:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:50:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:50:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:50:10 --> User ID: 6
DEBUG - 2025-06-01 13:50:10 --> Event ID: 8
DEBUG - 2025-06-01 13:50:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:50:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:50:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:50:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:50:30 --> SQL Query: 
DEBUG - 2025-06-01 13:50:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:50:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:50:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:50:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:51:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:51:19 --> SQL Query: 
DEBUG - 2025-06-01 13:53:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:53:21 --> Current Time for Certificate Query: 2025-06-01 13:53:21
DEBUG - 2025-06-01 13:53:21 --> SQL Query for Certificates: 
ERROR - 2025-06-01 13:53:21 --> mysqli_sql_exception: Not unique table/alias: 'events' in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT `events`...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `events`...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `events`...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `events`...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(928): CodeIgniter\Database\BaseBuilder->get()
#5 C:\sertifikat\myapp\app\Controllers\Events.php(302): CodeIgniter\Model->__call('get', Array)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Events->certificate()
#7 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#13 {main}
CRITICAL - 2025-06-01 13:53:21 --> CodeIgniter\Database\Exceptions\DatabaseException: Not unique table/alias: 'events'
[Method: GET, Route: events/certificate]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `events`.*, `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = :participants.user_id:
AND `events`.`end_date` < :events.end_date:
AND `participants`.`user_id` = :participants.user_id.1:
AND `events`.`end_date` < :events.end_date.1:', [...], false)
 2 SYSTEMPATH\Model.php(928): CodeIgniter\Database\BaseBuilder->get()
 3 APPPATH\Controllers\Events.php(302): CodeIgniter\Model->__call('get', [])
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->certificate()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
10 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-06-01 13:53:21 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Not unique table/alias: 'events'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `events`.*, `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = \'6\'
AND `events`.`end_date` < \'2025-06-01 13:53:21\'
AND `participants`.`user_id` = \'6\'
AND `events`.`end_date` < \'2025-06-01 13:53:21\'')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `events`.*, `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = \'6\'
AND `events`.`end_date` < \'2025-06-01 13:53:21\'
AND `participants`.`user_id` = \'6\'
AND `events`.`end_date` < \'2025-06-01 13:53:21\'')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `events`.*, `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = :participants.user_id:
AND `events`.`end_date` < :events.end_date:
AND `participants`.`user_id` = :participants.user_id.1:
AND `events`.`end_date` < :events.end_date.1:', [...], false)
 4 SYSTEMPATH\Model.php(928): CodeIgniter\Database\BaseBuilder->get()
 5 APPPATH\Controllers\Events.php(302): CodeIgniter\Model->__call('get', [])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->certificate()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-06-01 13:53:21 --> [Caused by] mysqli_sql_exception: Not unique table/alias: 'events'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT `events`.*, `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = \'6\'
AND `events`.`end_date` < \'2025-06-01 13:53:21\'
AND `participants`.`user_id` = \'6\'
AND `events`.`end_date` < \'2025-06-01 13:53:21\'', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT `events`.*, `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = \'6\'
AND `events`.`end_date` < \'2025-06-01 13:53:21\'
AND `participants`.`user_id` = \'6\'
AND `events`.`end_date` < \'2025-06-01 13:53:21\'')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT `events`.*, `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = \'6\'
AND `events`.`end_date` < \'2025-06-01 13:53:21\'
AND `participants`.`user_id` = \'6\'
AND `events`.`end_date` < \'2025-06-01 13:53:21\'')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT `events`.*, `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = :participants.user_id:
AND `events`.`end_date` < :events.end_date:
AND `participants`.`user_id` = :participants.user_id.1:
AND `events`.`end_date` < :events.end_date.1:', [...], false)
 5 SYSTEMPATH\Model.php(928): CodeIgniter\Database\BaseBuilder->get()
 6 APPPATH\Controllers\Events.php(302): CodeIgniter\Model->__call('get', [])
 7 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->certificate()
 8 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 9 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
11 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
12 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
13 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-06-01 13:54:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:54:17 --> Current Time for Certificate Query: 2025-06-01 13:54:17
DEBUG - 2025-06-01 13:54:17 --> SQL Query for Certificates: 
DEBUG - 2025-06-01 13:54:17 --> No registered and completed events found for user 6
DEBUG - 2025-06-01 13:54:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:54:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:54:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:54:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:54:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:54:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:54:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:54:28 --> Current Time for Certificate Query: 2025-06-01 13:54:28
DEBUG - 2025-06-01 13:54:28 --> SQL Query for Certificates: 
DEBUG - 2025-06-01 13:54:28 --> No registered and completed events found for user 6
DEBUG - 2025-06-01 13:54:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:54:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:54:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:54:32 --> Current Time for Certificate Query: 2025-06-01 13:54:32
DEBUG - 2025-06-01 13:54:32 --> SQL Query for Certificates: 
DEBUG - 2025-06-01 13:54:32 --> No registered and completed events found for user 6
DEBUG - 2025-06-01 13:54:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:55:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:55:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 13:55:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 14:00:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 14:00:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 14:00:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 14:00:07 --> Current Time for Certificate Query: 2025-06-01 14:00:07
DEBUG - 2025-06-01 14:00:07 --> SQL Query for Certificates: 
DEBUG - 2025-06-01 14:00:07 --> No registered and completed events found for user 6
DEBUG - 2025-06-01 14:00:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 14:00:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 14:00:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 14:00:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 14:00:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 14:00:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 14:00:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 14:00:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 14:00:32 --> Current Time for Certificate Query: 2025-06-01 14:00:32
DEBUG - 2025-06-01 14:00:32 --> SQL Query for Certificates: 
DEBUG - 2025-06-01 14:00:32 --> No registered and completed events found for user 6
DEBUG - 2025-06-01 14:00:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 14:00:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 14:00:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 14:00:35 --> Current Time for Certificate Query: 2025-06-01 14:00:35
DEBUG - 2025-06-01 14:00:35 --> SQL Query for Certificates: 
DEBUG - 2025-06-01 14:00:35 --> No registered and completed events found for user 6
DEBUG - 2025-06-01 14:02:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 14:02:17 --> Current Time for Certificate Query: 2025-06-01 14:02:17
DEBUG - 2025-06-01 14:02:17 --> SQL Query for Certificates: 
DEBUG - 2025-06-01 14:02:17 --> No registered and completed events found for user 6
DEBUG - 2025-06-01 14:02:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 14:02:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 14:02:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 14:02:19 --> Current Time for Certificate Query: 2025-06-01 14:02:19
DEBUG - 2025-06-01 14:02:19 --> SQL Query for Certificates: 
DEBUG - 2025-06-01 14:02:19 --> No registered and completed events found for user 6
DEBUG - 2025-06-01 14:02:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 14:02:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 14:02:22 --> Current Time for Certificate Query: 2025-06-01 14:02:22
DEBUG - 2025-06-01 14:02:22 --> SQL Query for Certificates: 
DEBUG - 2025-06-01 14:02:22 --> No registered and completed events found for user 6
DEBUG - 2025-06-01 14:03:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 14:03:40 --> Current Time for Certificate Query: 2025-06-01 14:03:40
DEBUG - 2025-06-01 14:03:40 --> SQL Query for Certificates: 
DEBUG - 2025-06-01 14:03:40 --> No registered and completed events found for user 6
DEBUG - 2025-06-01 14:04:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 14:04:38 --> Current Time for Certificate Query: 2025-06-01 14:04:38
DEBUG - 2025-06-01 14:04:38 --> SQL Query for Certificates: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '6'
AND `events`.`end_date` < '2025-06-01 14:04:38'
DEBUG - 2025-06-01 14:04:38 --> No registered and completed events found for user 6
DEBUG - 2025-06-01 14:04:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 14:04:41 --> Current Time for Certificate Query: 2025-06-01 14:04:41
DEBUG - 2025-06-01 14:04:41 --> SQL Query for Certificates: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '6'
AND `events`.`end_date` < '2025-06-01 14:04:41'
DEBUG - 2025-06-01 14:04:41 --> No registered and completed events found for user 6
DEBUG - 2025-06-01 21:05:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:05:27 --> Current Time for Certificate Query: 2025-06-01 21:05:27
DEBUG - 2025-06-01 21:05:27 --> SQL Query for Certificates: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '6'
AND `events`.`end_date` < '2025-06-01 21:05:27'
DEBUG - 2025-06-01 21:05:27 --> Found Event for Certificate: ID=8, Title=seminar bareng gyj, EndDate=2025-06-01 20:59:00
DEBUG - 2025-06-01 21:05:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:05:30 --> User ID: 6
DEBUG - 2025-06-01 21:05:30 --> Event ID: 8
DEBUG - 2025-06-01 21:05:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:05:34 --> Current Time for Certificate Query: 2025-06-01 21:05:34
DEBUG - 2025-06-01 21:05:34 --> SQL Query for Certificates: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '6'
AND `events`.`end_date` < '2025-06-01 21:05:34'
DEBUG - 2025-06-01 21:05:34 --> Found Event for Certificate: ID=8, Title=seminar bareng gyj, EndDate=2025-06-01 20:59:00
DEBUG - 2025-06-01 21:05:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:05:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:05:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:05:39 --> Current Time for Certificate Query: 2025-06-01 21:05:39
DEBUG - 2025-06-01 21:05:39 --> SQL Query for Certificates: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '6'
AND `events`.`end_date` < '2025-06-01 21:05:39'
DEBUG - 2025-06-01 21:05:39 --> Found Event for Certificate: ID=8, Title=seminar bareng gyj, EndDate=2025-06-01 20:59:00
DEBUG - 2025-06-01 21:05:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:08:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:08:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:08:01 --> Current Time for Certificate Query: 2025-06-01 21:08:01
DEBUG - 2025-06-01 21:08:01 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '6'
DEBUG - 2025-06-01 21:08:01 --> Event: seminar bareng gyj, EndDate: 2025-06-01 20:59:00, IsCompleted: true
DEBUG - 2025-06-01 21:08:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:08:04 --> Current Time for Certificate Query: 2025-06-01 21:08:04
DEBUG - 2025-06-01 21:08:04 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '6'
DEBUG - 2025-06-01 21:08:04 --> Event: seminar bareng gyj, EndDate: 2025-06-01 20:59:00, IsCompleted: true
DEBUG - 2025-06-01 21:08:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:08:08 --> User ID: 6
DEBUG - 2025-06-01 21:08:08 --> Event ID: 8
DEBUG - 2025-06-01 21:09:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:09:21 --> User ID: 6
DEBUG - 2025-06-01 21:09:21 --> Event ID: 8
DEBUG - 2025-06-01 21:09:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:09:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:09:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:09:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:09:35 --> Current Time for Certificate Query: 2025-06-01 21:09:35
DEBUG - 2025-06-01 21:09:35 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '6'
DEBUG - 2025-06-01 21:09:35 --> Event: seminar bareng gyj, EndDate: 2025-06-01 20:59:00, IsCompleted: true
DEBUG - 2025-06-01 21:09:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:09:37 --> User ID: 6
DEBUG - 2025-06-01 21:09:37 --> Event ID: 8
DEBUG - 2025-06-01 21:10:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:10:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:10:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:10:05 --> Current Time for Certificate Query: 2025-06-01 21:10:05
DEBUG - 2025-06-01 21:10:05 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '6'
DEBUG - 2025-06-01 21:10:05 --> Event: seminar bareng gyj, EndDate: 2025-06-01 20:59:00, IsCompleted: true
DEBUG - 2025-06-01 21:10:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:10:06 --> User ID: 6
DEBUG - 2025-06-01 21:10:06 --> Event ID: 8
DEBUG - 2025-06-01 21:10:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:10:15 --> User ID: 6
DEBUG - 2025-06-01 21:10:15 --> Event ID: 8
ERROR - 2025-06-01 21:15:16 --> mysqli_sql_exception: Table 'participants' already exists in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('CREATE TABLE `p...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('CREATE TABLE `p...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('CREATE TABLE `p...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\Forge.php(570): CodeIgniter\Database\BaseConnection->query('CREATE TABLE `p...')
#4 C:\sertifikat\myapp\app\Database\Migrations\2025-06-01-081000_CreateParticipantsTable.php(53): CodeIgniter\Database\Forge->createTable('participants')
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(876): App\Database\Migrations\CreateParticipantsTable->up()
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(182): CodeIgniter\Database\MigrationRunner->migrate('up', Object(stdClass))
#7 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Commands\Database\Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest(NULL)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Commands.php(74): CodeIgniter\Commands\Database\Migrate->run(Array)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Console.php(47): CodeIgniter\CLI\Commands->run('migrate', Array)
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(360): CodeIgniter\CLI\Console->run()
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#12 C:\sertifikat\myapp\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#13 {main}
DEBUG - 2025-06-01 21:20:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:20:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:23:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:23:18 --> User ID: 6
DEBUG - 2025-06-01 21:23:18 --> Event ID: 8
DEBUG - 2025-06-01 21:23:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:23:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:23:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:23:26 --> Current Time for Certificate Query: 2025-06-01 21:23:26
DEBUG - 2025-06-01 21:23:26 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '6'
DEBUG - 2025-06-01 21:23:26 --> Event: seminar bareng gyj, EndDate: 2025-06-01 20:59:00, IsCompleted: true
DEBUG - 2025-06-01 21:23:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:23:30 --> Current Time for Certificate Query: 2025-06-01 21:23:30
DEBUG - 2025-06-01 21:23:30 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '6'
DEBUG - 2025-06-01 21:23:30 --> Event: seminar bareng gyj, EndDate: 2025-06-01 20:59:00, IsCompleted: true
DEBUG - 2025-06-01 21:23:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:23:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:23:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:23:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:27:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-01 21:27:43 --> mysqli_sql_exception: Unknown column 'signer1_name' in 'field list' in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `ev...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `ev...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `ev...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `ev...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\BaseModel.php(843): CodeIgniter\Model->doInsert(Array)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(800): CodeIgniter\BaseModel->insert(Array, true)
#7 C:\sertifikat\myapp\app\Controllers\Events.php(140): CodeIgniter\Model->insert(Array)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Events->store()
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#15 {main}
CRITICAL - 2025-06-01 21:27:43 --> CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'signer1_name' in 'field list'
[Method: POST, Route: events/store]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 2 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 3 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 4 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 5 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-06-01 21:27:43 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'signer1_name' in 'field list'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748788063_7440e9f8c2e439bfc917.png\', \'signatures/1748788063_175007739afabc64d6e5.png\', \'2025-06-01 21:27:43\', \'2025-06-01 21:27:43\')')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748788063_7440e9f8c2e439bfc917.png\', \'signatures/1748788063_175007739afabc64d6e5.png\', \'2025-06-01 21:27:43\', \'2025-06-01 21:27:43\')')
 3 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 4 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 5 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 6 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 7 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-06-01 21:27:43 --> [Caused by] mysqli_sql_exception: Unknown column 'signer1_name' in 'field list'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748788063_7440e9f8c2e439bfc917.png\', \'signatures/1748788063_175007739afabc64d6e5.png\', \'2025-06-01 21:27:43\', \'2025-06-01 21:27:43\')', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748788063_7440e9f8c2e439bfc917.png\', \'signatures/1748788063_175007739afabc64d6e5.png\', \'2025-06-01 21:27:43\', \'2025-06-01 21:27:43\')')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748788063_7440e9f8c2e439bfc917.png\', \'signatures/1748788063_175007739afabc64d6e5.png\', \'2025-06-01 21:27:43\', \'2025-06-01 21:27:43\')')
 4 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 5 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 6 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 7 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 8 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
15 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
ERROR - 2025-06-01 21:28:43 --> mysqli_sql_exception: Table 'participants' already exists in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('CREATE TABLE `p...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('CREATE TABLE `p...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('CREATE TABLE `p...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\Forge.php(570): CodeIgniter\Database\BaseConnection->query('CREATE TABLE `p...')
#4 C:\sertifikat\myapp\app\Database\Migrations\2025-06-01-081000_CreateParticipantsTable.php(53): CodeIgniter\Database\Forge->createTable('participants')
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(876): App\Database\Migrations\CreateParticipantsTable->up()
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(182): CodeIgniter\Database\MigrationRunner->migrate('up', Object(stdClass))
#7 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Commands\Database\Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest(NULL)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Commands.php(74): CodeIgniter\Commands\Database\Migrate->run(Array)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Console.php(47): CodeIgniter\CLI\Commands->run('migrate', Array)
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(360): CodeIgniter\CLI\Console->run()
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#12 C:\sertifikat\myapp\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#13 {main}
DEBUG - 2025-06-01 21:28:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:28:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:29:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-01 21:29:04 --> mysqli_sql_exception: Unknown column 'signer1_name' in 'field list' in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `ev...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `ev...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `ev...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `ev...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\BaseModel.php(843): CodeIgniter\Model->doInsert(Array)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(800): CodeIgniter\BaseModel->insert(Array, true)
#7 C:\sertifikat\myapp\app\Controllers\Events.php(140): CodeIgniter\Model->insert(Array)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Events->store()
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#15 {main}
CRITICAL - 2025-06-01 21:29:04 --> CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'signer1_name' in 'field list'
[Method: POST, Route: events/store]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 2 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 3 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 4 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 5 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-06-01 21:29:04 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'signer1_name' in 'field list'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748788144_09e1f615ca5f3860f086.png\', \'signatures/1748788144_acfb9bc4def980dcef94.png\', \'2025-06-01 21:29:04\', \'2025-06-01 21:29:04\')')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748788144_09e1f615ca5f3860f086.png\', \'signatures/1748788144_acfb9bc4def980dcef94.png\', \'2025-06-01 21:29:04\', \'2025-06-01 21:29:04\')')
 3 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 4 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 5 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 6 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 7 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-06-01 21:29:04 --> [Caused by] mysqli_sql_exception: Unknown column 'signer1_name' in 'field list'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748788144_09e1f615ca5f3860f086.png\', \'signatures/1748788144_acfb9bc4def980dcef94.png\', \'2025-06-01 21:29:04\', \'2025-06-01 21:29:04\')', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748788144_09e1f615ca5f3860f086.png\', \'signatures/1748788144_acfb9bc4def980dcef94.png\', \'2025-06-01 21:29:04\', \'2025-06-01 21:29:04\')')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748788144_09e1f615ca5f3860f086.png\', \'signatures/1748788144_acfb9bc4def980dcef94.png\', \'2025-06-01 21:29:04\', \'2025-06-01 21:29:04\')')
 4 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 5 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 6 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 7 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 8 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
15 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
ERROR - 2025-06-01 21:31:53 --> mysqli_sql_exception: Table 'participants' already exists in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('CREATE TABLE `p...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('CREATE TABLE `p...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('CREATE TABLE `p...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\Forge.php(570): CodeIgniter\Database\BaseConnection->query('CREATE TABLE `p...')
#4 C:\sertifikat\myapp\app\Database\Migrations\2025-06-01-081000_CreateParticipantsTable.php(53): CodeIgniter\Database\Forge->createTable('participants')
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(876): App\Database\Migrations\CreateParticipantsTable->up()
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(182): CodeIgniter\Database\MigrationRunner->migrate('up', Object(stdClass))
#7 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Commands\Database\Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest(NULL)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Commands.php(74): CodeIgniter\Commands\Database\Migrate->run(Array)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Console.php(47): CodeIgniter\CLI\Commands->run('migrate', Array)
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(360): CodeIgniter\CLI\Console->run()
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#12 C:\sertifikat\myapp\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#13 {main}
DEBUG - 2025-06-01 21:32:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-01 21:32:54 --> mysqli_sql_exception: Unknown column 'signer1_name' in 'field list' in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `ev...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `ev...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `ev...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `ev...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\BaseModel.php(843): CodeIgniter\Model->doInsert(Array)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(800): CodeIgniter\BaseModel->insert(Array, true)
#7 C:\sertifikat\myapp\app\Controllers\Events.php(140): CodeIgniter\Model->insert(Array)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Events->store()
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#15 {main}
CRITICAL - 2025-06-01 21:32:54 --> CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'signer1_name' in 'field list'
[Method: POST, Route: events/store]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 2 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 3 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 4 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 5 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-06-01 21:32:54 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'signer1_name' in 'field list'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748788374_feb720b42743a1ca2657.png\', \'signatures/1748788374_396b147d330566c8c1d5.png\', \'2025-06-01 21:32:54\', \'2025-06-01 21:32:54\')')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748788374_feb720b42743a1ca2657.png\', \'signatures/1748788374_396b147d330566c8c1d5.png\', \'2025-06-01 21:32:54\', \'2025-06-01 21:32:54\')')
 3 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 4 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 5 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 6 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 7 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-06-01 21:32:54 --> [Caused by] mysqli_sql_exception: Unknown column 'signer1_name' in 'field list'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748788374_feb720b42743a1ca2657.png\', \'signatures/1748788374_396b147d330566c8c1d5.png\', \'2025-06-01 21:32:54\', \'2025-06-01 21:32:54\')', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748788374_feb720b42743a1ca2657.png\', \'signatures/1748788374_396b147d330566c8c1d5.png\', \'2025-06-01 21:32:54\', \'2025-06-01 21:32:54\')')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748788374_feb720b42743a1ca2657.png\', \'signatures/1748788374_396b147d330566c8c1d5.png\', \'2025-06-01 21:32:54\', \'2025-06-01 21:32:54\')')
 4 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 5 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 6 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 7 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 8 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
15 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-06-01 21:32:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:32:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-01 21:32:59 --> mysqli_sql_exception: Unknown column 'signer1_name' in 'field list' in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `ev...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `ev...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `ev...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `ev...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\BaseModel.php(843): CodeIgniter\Model->doInsert(Array)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(800): CodeIgniter\BaseModel->insert(Array, true)
#7 C:\sertifikat\myapp\app\Controllers\Events.php(140): CodeIgniter\Model->insert(Array)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Events->store()
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#15 {main}
CRITICAL - 2025-06-01 21:32:59 --> CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'signer1_name' in 'field list'
[Method: POST, Route: events/store]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 2 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 3 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 4 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 5 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-06-01 21:32:59 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'signer1_name' in 'field list'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748788379_1c380945250f4421e12e.png\', \'signatures/1748788379_138b20f8ca18ef752567.png\', \'2025-06-01 21:32:59\', \'2025-06-01 21:32:59\')')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748788379_1c380945250f4421e12e.png\', \'signatures/1748788379_138b20f8ca18ef752567.png\', \'2025-06-01 21:32:59\', \'2025-06-01 21:32:59\')')
 3 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 4 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 5 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 6 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 7 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-06-01 21:32:59 --> [Caused by] mysqli_sql_exception: Unknown column 'signer1_name' in 'field list'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748788379_1c380945250f4421e12e.png\', \'signatures/1748788379_138b20f8ca18ef752567.png\', \'2025-06-01 21:32:59\', \'2025-06-01 21:32:59\')', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748788379_1c380945250f4421e12e.png\', \'signatures/1748788379_138b20f8ca18ef752567.png\', \'2025-06-01 21:32:59\', \'2025-06-01 21:32:59\')')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748788379_1c380945250f4421e12e.png\', \'signatures/1748788379_138b20f8ca18ef752567.png\', \'2025-06-01 21:32:59\', \'2025-06-01 21:32:59\')')
 4 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 5 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 6 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 7 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 8 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
15 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-06-01 21:34:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-01 21:34:54 --> mysqli_sql_exception: Unknown column 'signer1_name' in 'field list' in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `ev...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `ev...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `ev...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `ev...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\BaseModel.php(843): CodeIgniter\Model->doInsert(Array)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(800): CodeIgniter\BaseModel->insert(Array, true)
#7 C:\sertifikat\myapp\app\Controllers\Events.php(140): CodeIgniter\Model->insert(Array)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Events->store()
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#15 {main}
CRITICAL - 2025-06-01 21:34:54 --> CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'signer1_name' in 'field list'
[Method: POST, Route: events/store]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 2 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 3 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 4 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 5 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-06-01 21:34:54 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'signer1_name' in 'field list'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748788494_1cfa99c2fdf1675fc6f7.png\', \'signatures/1748788494_4851ff0c918523b05825.png\', \'2025-06-01 21:34:54\', \'2025-06-01 21:34:54\')')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748788494_1cfa99c2fdf1675fc6f7.png\', \'signatures/1748788494_4851ff0c918523b05825.png\', \'2025-06-01 21:34:54\', \'2025-06-01 21:34:54\')')
 3 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 4 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 5 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 6 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 7 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-06-01 21:34:54 --> [Caused by] mysqli_sql_exception: Unknown column 'signer1_name' in 'field list'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748788494_1cfa99c2fdf1675fc6f7.png\', \'signatures/1748788494_4851ff0c918523b05825.png\', \'2025-06-01 21:34:54\', \'2025-06-01 21:34:54\')', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748788494_1cfa99c2fdf1675fc6f7.png\', \'signatures/1748788494_4851ff0c918523b05825.png\', \'2025-06-01 21:34:54\', \'2025-06-01 21:34:54\')')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748788494_1cfa99c2fdf1675fc6f7.png\', \'signatures/1748788494_4851ff0c918523b05825.png\', \'2025-06-01 21:34:54\', \'2025-06-01 21:34:54\')')
 4 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 5 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 6 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 7 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 8 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
15 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-06-01 21:51:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-01 21:51:04 --> mysqli_sql_exception: Unknown column 'signer1_name' in 'field list' in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `ev...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `ev...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `ev...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `ev...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\BaseModel.php(843): CodeIgniter\Model->doInsert(Array)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(800): CodeIgniter\BaseModel->insert(Array, true)
#7 C:\sertifikat\myapp\app\Controllers\Events.php(140): CodeIgniter\Model->insert(Array)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Events->store()
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#15 {main}
CRITICAL - 2025-06-01 21:51:04 --> CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'signer1_name' in 'field list'
[Method: POST, Route: events/store]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 2 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 3 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 4 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 5 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-06-01 21:51:04 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'signer1_name' in 'field list'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748789464_e2b3ee6120d4c224856f.png\', \'signatures/1748789464_5084ceb8c1caf5da8167.png\', \'2025-06-01 21:51:04\', \'2025-06-01 21:51:04\')')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748789464_e2b3ee6120d4c224856f.png\', \'signatures/1748789464_5084ceb8c1caf5da8167.png\', \'2025-06-01 21:51:04\', \'2025-06-01 21:51:04\')')
 3 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 4 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 5 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 6 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 7 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-06-01 21:51:04 --> [Caused by] mysqli_sql_exception: Unknown column 'signer1_name' in 'field list'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748789464_e2b3ee6120d4c224856f.png\', \'signatures/1748789464_5084ceb8c1caf5da8167.png\', \'2025-06-01 21:51:04\', \'2025-06-01 21:51:04\')', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748789464_e2b3ee6120d4c224856f.png\', \'signatures/1748789464_5084ceb8c1caf5da8167.png\', \'2025-06-01 21:51:04\', \'2025-06-01 21:51:04\')')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748789464_e2b3ee6120d4c224856f.png\', \'signatures/1748789464_5084ceb8c1caf5da8167.png\', \'2025-06-01 21:51:04\', \'2025-06-01 21:51:04\')')
 4 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 5 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 6 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 7 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 8 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
15 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
ERROR - 2025-06-01 21:51:40 --> mysqli_sql_exception: Table 'participants' already exists in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('CREATE TABLE `p...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('CREATE TABLE `p...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('CREATE TABLE `p...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\Forge.php(570): CodeIgniter\Database\BaseConnection->query('CREATE TABLE `p...')
#4 C:\sertifikat\myapp\app\Database\Migrations\2025-06-01-081000_CreateParticipantsTable.php(53): CodeIgniter\Database\Forge->createTable('participants')
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(876): App\Database\Migrations\CreateParticipantsTable->up()
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(182): CodeIgniter\Database\MigrationRunner->migrate('up', Object(stdClass))
#7 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Commands\Database\Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest(NULL)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Commands.php(74): CodeIgniter\Commands\Database\Migrate->run(Array)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Console.php(47): CodeIgniter\CLI\Commands->run('migrate', Array)
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(360): CodeIgniter\CLI\Console->run()
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#12 C:\sertifikat\myapp\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#13 {main}
DEBUG - 2025-06-01 21:52:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-01 21:52:04 --> mysqli_sql_exception: Unknown column 'signer1_name' in 'field list' in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `ev...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `ev...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `ev...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `ev...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\BaseModel.php(843): CodeIgniter\Model->doInsert(Array)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(800): CodeIgniter\BaseModel->insert(Array, true)
#7 C:\sertifikat\myapp\app\Controllers\Events.php(140): CodeIgniter\Model->insert(Array)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Events->store()
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#15 {main}
CRITICAL - 2025-06-01 21:52:04 --> CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'signer1_name' in 'field list'
[Method: POST, Route: events/store]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 2 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 3 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 4 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 5 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-06-01 21:52:04 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'signer1_name' in 'field list'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748789524_3ccbe65e03e41ec84ad0.png\', \'signatures/1748789524_f517aae4722c561dd028.png\', \'2025-06-01 21:52:04\', \'2025-06-01 21:52:04\')')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748789524_3ccbe65e03e41ec84ad0.png\', \'signatures/1748789524_f517aae4722c561dd028.png\', \'2025-06-01 21:52:04\', \'2025-06-01 21:52:04\')')
 3 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 4 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 5 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 6 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 7 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-06-01 21:52:04 --> [Caused by] mysqli_sql_exception: Unknown column 'signer1_name' in 'field list'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748789524_3ccbe65e03e41ec84ad0.png\', \'signatures/1748789524_f517aae4722c561dd028.png\', \'2025-06-01 21:52:04\', \'2025-06-01 21:52:04\')', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748789524_3ccbe65e03e41ec84ad0.png\', \'signatures/1748789524_f517aae4722c561dd028.png\', \'2025-06-01 21:52:04\', \'2025-06-01 21:52:04\')')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748789524_3ccbe65e03e41ec84ad0.png\', \'signatures/1748789524_f517aae4722c561dd028.png\', \'2025-06-01 21:52:04\', \'2025-06-01 21:52:04\')')
 4 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 5 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 6 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 7 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 8 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
15 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
ERROR - 2025-06-01 21:52:26 --> mysqli_sql_exception: Table 'participants' already exists in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('CREATE TABLE `p...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('CREATE TABLE `p...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('CREATE TABLE `p...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\Forge.php(570): CodeIgniter\Database\BaseConnection->query('CREATE TABLE `p...')
#4 C:\sertifikat\myapp\app\Database\Migrations\2025-06-01-081000_CreateParticipantsTable.php(53): CodeIgniter\Database\Forge->createTable('participants')
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(876): App\Database\Migrations\CreateParticipantsTable->up()
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(182): CodeIgniter\Database\MigrationRunner->migrate('up', Object(stdClass))
#7 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Commands\Database\Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest(NULL)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Commands.php(74): CodeIgniter\Commands\Database\Migrate->run(Array)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Console.php(47): CodeIgniter\CLI\Commands->run('migrate', Array)
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(360): CodeIgniter\CLI\Console->run()
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#12 C:\sertifikat\myapp\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#13 {main}
DEBUG - 2025-06-01 21:52:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-01 21:52:45 --> mysqli_sql_exception: Unknown column 'signer1_name' in 'field list' in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `ev...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `ev...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `ev...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `ev...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\BaseModel.php(843): CodeIgniter\Model->doInsert(Array)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(800): CodeIgniter\BaseModel->insert(Array, true)
#7 C:\sertifikat\myapp\app\Controllers\Events.php(140): CodeIgniter\Model->insert(Array)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Events->store()
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#15 {main}
CRITICAL - 2025-06-01 21:52:45 --> CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'signer1_name' in 'field list'
[Method: POST, Route: events/store]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 2 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 3 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 4 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 5 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-06-01 21:52:45 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'signer1_name' in 'field list'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748789565_0ada37b2f716056327ee.png\', \'signatures/1748789565_deefc9271297597e8320.png\', \'2025-06-01 21:52:45\', \'2025-06-01 21:52:45\')')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748789565_0ada37b2f716056327ee.png\', \'signatures/1748789565_deefc9271297597e8320.png\', \'2025-06-01 21:52:45\', \'2025-06-01 21:52:45\')')
 3 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 4 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 5 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 6 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 7 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-06-01 21:52:45 --> [Caused by] mysqli_sql_exception: Unknown column 'signer1_name' in 'field list'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748789565_0ada37b2f716056327ee.png\', \'signatures/1748789565_deefc9271297597e8320.png\', \'2025-06-01 21:52:45\', \'2025-06-01 21:52:45\')', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748789565_0ada37b2f716056327ee.png\', \'signatures/1748789565_deefc9271297597e8320.png\', \'2025-06-01 21:52:45\', \'2025-06-01 21:52:45\')')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748789565_0ada37b2f716056327ee.png\', \'signatures/1748789565_deefc9271297597e8320.png\', \'2025-06-01 21:52:45\', \'2025-06-01 21:52:45\')')
 4 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 5 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 6 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 7 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 8 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
15 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-06-01 21:53:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:53:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:53:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:53:18 --> Current Time for Certificate Query: 2025-06-01 21:53:18
DEBUG - 2025-06-01 21:53:18 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '6'
DEBUG - 2025-06-01 21:53:18 --> Event: seminar bareng gyj, EndDate: 2025-06-01 20:59:00, IsCompleted: true
DEBUG - 2025-06-01 21:53:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:53:19 --> User ID: 6
DEBUG - 2025-06-01 21:53:19 --> Event ID: 8
DEBUG - 2025-06-01 21:56:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:56:37 --> User ID: 6
DEBUG - 2025-06-01 21:56:37 --> Event ID: 8
DEBUG - 2025-06-01 21:56:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:56:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:56:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 21:56:43 --> Current Time for Certificate Query: 2025-06-01 21:56:43
DEBUG - 2025-06-01 21:56:43 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '6'
DEBUG - 2025-06-01 21:56:43 --> Event: seminar bareng gyj, EndDate: 2025-06-01 20:59:00, IsCompleted: true
ERROR - 2025-06-01 21:58:57 --> mysqli_sql_exception: Table 'participants' already exists in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('CREATE TABLE `p...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('CREATE TABLE `p...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('CREATE TABLE `p...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\Forge.php(570): CodeIgniter\Database\BaseConnection->query('CREATE TABLE `p...')
#4 C:\sertifikat\myapp\app\Database\Migrations\2025-06-01-081000_CreateParticipantsTable.php(53): CodeIgniter\Database\Forge->createTable('participants')
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(876): App\Database\Migrations\CreateParticipantsTable->up()
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(182): CodeIgniter\Database\MigrationRunner->migrate('up', Object(stdClass))
#7 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Commands\Database\Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest(NULL)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Commands.php(74): CodeIgniter\Commands\Database\Migrate->run(Array)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Console.php(47): CodeIgniter\CLI\Commands->run('migrate', Array)
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(360): CodeIgniter\CLI\Console->run()
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#12 C:\sertifikat\myapp\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#13 {main}
DEBUG - 2025-06-01 21:59:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-01 21:59:05 --> mysqli_sql_exception: Unknown column 'signer1_name' in 'field list' in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `ev...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `ev...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `ev...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `ev...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\BaseModel.php(843): CodeIgniter\Model->doInsert(Array)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(800): CodeIgniter\BaseModel->insert(Array, true)
#7 C:\sertifikat\myapp\app\Controllers\Events.php(140): CodeIgniter\Model->insert(Array)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Events->store()
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#15 {main}
CRITICAL - 2025-06-01 21:59:05 --> CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'signer1_name' in 'field list'
[Method: POST, Route: events/store]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 2 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 3 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 4 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 5 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-06-01 21:59:05 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'signer1_name' in 'field list'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748789945_9e6e3fda500130229c8e.png\', \'signatures/1748789945_d29f8ef5f7518ffc220f.png\', \'2025-06-01 21:59:05\', \'2025-06-01 21:59:05\')')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748789945_9e6e3fda500130229c8e.png\', \'signatures/1748789945_d29f8ef5f7518ffc220f.png\', \'2025-06-01 21:59:05\', \'2025-06-01 21:59:05\')')
 3 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 4 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 5 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 6 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 7 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-06-01 21:59:05 --> [Caused by] mysqli_sql_exception: Unknown column 'signer1_name' in 'field list'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748789945_9e6e3fda500130229c8e.png\', \'signatures/1748789945_d29f8ef5f7518ffc220f.png\', \'2025-06-01 21:59:05\', \'2025-06-01 21:59:05\')', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748789945_9e6e3fda500130229c8e.png\', \'signatures/1748789945_d29f8ef5f7518ffc220f.png\', \'2025-06-01 21:59:05\', \'2025-06-01 21:59:05\')')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748789945_9e6e3fda500130229c8e.png\', \'signatures/1748789945_d29f8ef5f7518ffc220f.png\', \'2025-06-01 21:59:05\', \'2025-06-01 21:59:05\')')
 4 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 5 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 6 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 7 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 8 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
15 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-06-01 21:59:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-01 21:59:49 --> mysqli_sql_exception: Unknown column 'signer1_name' in 'field list' in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `ev...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `ev...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `ev...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `ev...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\BaseModel.php(843): CodeIgniter\Model->doInsert(Array)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(800): CodeIgniter\BaseModel->insert(Array, true)
#7 C:\sertifikat\myapp\app\Controllers\Events.php(140): CodeIgniter\Model->insert(Array)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Events->store()
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#15 {main}
CRITICAL - 2025-06-01 21:59:49 --> CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'signer1_name' in 'field list'
[Method: POST, Route: events/store]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 2 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 3 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 4 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 5 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-06-01 21:59:49 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'signer1_name' in 'field list'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748789989_f98b2d31427fe9ac6dd1.png\', \'signatures/1748789989_f23d80b1d05cc4b8ee10.png\', \'2025-06-01 21:59:49\', \'2025-06-01 21:59:49\')')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748789989_f98b2d31427fe9ac6dd1.png\', \'signatures/1748789989_f23d80b1d05cc4b8ee10.png\', \'2025-06-01 21:59:49\', \'2025-06-01 21:59:49\')')
 3 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 4 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 5 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 6 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 7 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-06-01 21:59:49 --> [Caused by] mysqli_sql_exception: Unknown column 'signer1_name' in 'field list'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748789989_f98b2d31427fe9ac6dd1.png\', \'signatures/1748789989_f23d80b1d05cc4b8ee10.png\', \'2025-06-01 21:59:49\', \'2025-06-01 21:59:49\')', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748789989_f98b2d31427fe9ac6dd1.png\', \'signatures/1748789989_f23d80b1d05cc4b8ee10.png\', \'2025-06-01 21:59:49\', \'2025-06-01 21:59:49\')')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar Bareng Andika setiawan\', \'Seminar Bareng Andika setiawan \', \'2025-06-01T21:29\', \'2025-06-01T21:41\', \'Cikarang\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748789989_f98b2d31427fe9ac6dd1.png\', \'signatures/1748789989_f23d80b1d05cc4b8ee10.png\', \'2025-06-01 21:59:49\', \'2025-06-01 21:59:49\')')
 4 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 5 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 6 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 7 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 8 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
15 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-06-01 22:02:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:02:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:03:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:03:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:04:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-01 22:04:27 --> mysqli_sql_exception: Unknown column 'signer1_name' in 'field list' in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `ev...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `ev...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `ev...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `ev...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\BaseModel.php(843): CodeIgniter\Model->doInsert(Array)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(800): CodeIgniter\BaseModel->insert(Array, true)
#7 C:\sertifikat\myapp\app\Controllers\Events.php(140): CodeIgniter\Model->insert(Array)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Events->store()
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#15 {main}
CRITICAL - 2025-06-01 22:04:27 --> CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'signer1_name' in 'field list'
[Method: POST, Route: events/store]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 2 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 3 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 4 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 5 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-06-01 22:04:27 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'signer1_name' in 'field list'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar bareng DIKA\', \'Seminar bareng DIKA\', \'2025-06-01T22:06\', \'2025-06-01T22:30\', \'Bandung\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748790267_8284b271a9c31bdda70d.png\', \'signatures/1748790267_bcab0798f400125d12d7.png\', \'2025-06-01 22:04:27\', \'2025-06-01 22:04:27\')')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar bareng DIKA\', \'Seminar bareng DIKA\', \'2025-06-01T22:06\', \'2025-06-01T22:30\', \'Bandung\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748790267_8284b271a9c31bdda70d.png\', \'signatures/1748790267_bcab0798f400125d12d7.png\', \'2025-06-01 22:04:27\', \'2025-06-01 22:04:27\')')
 3 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 4 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 5 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 6 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 7 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-06-01 22:04:27 --> [Caused by] mysqli_sql_exception: Unknown column 'signer1_name' in 'field list'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar bareng DIKA\', \'Seminar bareng DIKA\', \'2025-06-01T22:06\', \'2025-06-01T22:30\', \'Bandung\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748790267_8284b271a9c31bdda70d.png\', \'signatures/1748790267_bcab0798f400125d12d7.png\', \'2025-06-01 22:04:27\', \'2025-06-01 22:04:27\')', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar bareng DIKA\', \'Seminar bareng DIKA\', \'2025-06-01T22:06\', \'2025-06-01T22:30\', \'Bandung\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748790267_8284b271a9c31bdda70d.png\', \'signatures/1748790267_bcab0798f400125d12d7.png\', \'2025-06-01 22:04:27\', \'2025-06-01 22:04:27\')')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar bareng DIKA\', \'Seminar bareng DIKA\', \'2025-06-01T22:06\', \'2025-06-01T22:30\', \'Bandung\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748790267_8284b271a9c31bdda70d.png\', \'signatures/1748790267_bcab0798f400125d12d7.png\', \'2025-06-01 22:04:27\', \'2025-06-01 22:04:27\')')
 4 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 5 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 6 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 7 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 8 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
15 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
ERROR - 2025-06-01 22:05:13 --> mysqli_sql_exception: Table 'participants' already exists in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('CREATE TABLE `p...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('CREATE TABLE `p...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('CREATE TABLE `p...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\Forge.php(570): CodeIgniter\Database\BaseConnection->query('CREATE TABLE `p...')
#4 C:\sertifikat\myapp\app\Database\Migrations\2025-06-01-081000_CreateParticipantsTable.php(53): CodeIgniter\Database\Forge->createTable('participants')
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(876): App\Database\Migrations\CreateParticipantsTable->up()
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(182): CodeIgniter\Database\MigrationRunner->migrate('up', Object(stdClass))
#7 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Commands\Database\Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest(NULL)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Commands.php(74): CodeIgniter\Commands\Database\Migrate->run(Array)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Console.php(47): CodeIgniter\CLI\Commands->run('migrate', Array)
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(360): CodeIgniter\CLI\Console->run()
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#12 C:\sertifikat\myapp\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#13 {main}
DEBUG - 2025-06-01 22:06:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-01 22:06:27 --> mysqli_sql_exception: Unknown column 'signer1_name' in 'field list' in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `ev...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `ev...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `ev...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `ev...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\BaseModel.php(843): CodeIgniter\Model->doInsert(Array)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(800): CodeIgniter\BaseModel->insert(Array, true)
#7 C:\sertifikat\myapp\app\Controllers\Events.php(140): CodeIgniter\Model->insert(Array)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Events->store()
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#15 {main}
CRITICAL - 2025-06-01 22:06:27 --> CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'signer1_name' in 'field list'
[Method: POST, Route: events/store]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 2 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 3 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 4 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 5 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-06-01 22:06:27 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'signer1_name' in 'field list'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar bareng DIKA\', \'Seminar bareng DIKA\', \'2025-06-01T22:06\', \'2025-06-01T22:30\', \'Bandung\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748790387_98880db0aa38e02ab614.png\', \'signatures/1748790387_2b61c59d27719d810206.png\', \'2025-06-01 22:06:27\', \'2025-06-01 22:06:27\')')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar bareng DIKA\', \'Seminar bareng DIKA\', \'2025-06-01T22:06\', \'2025-06-01T22:30\', \'Bandung\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748790387_98880db0aa38e02ab614.png\', \'signatures/1748790387_2b61c59d27719d810206.png\', \'2025-06-01 22:06:27\', \'2025-06-01 22:06:27\')')
 3 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 4 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 5 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 6 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 7 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-06-01 22:06:27 --> [Caused by] mysqli_sql_exception: Unknown column 'signer1_name' in 'field list'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar bareng DIKA\', \'Seminar bareng DIKA\', \'2025-06-01T22:06\', \'2025-06-01T22:30\', \'Bandung\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748790387_98880db0aa38e02ab614.png\', \'signatures/1748790387_2b61c59d27719d810206.png\', \'2025-06-01 22:06:27\', \'2025-06-01 22:06:27\')', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar bareng DIKA\', \'Seminar bareng DIKA\', \'2025-06-01T22:06\', \'2025-06-01T22:30\', \'Bandung\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748790387_98880db0aa38e02ab614.png\', \'signatures/1748790387_2b61c59d27719d810206.png\', \'2025-06-01 22:06:27\', \'2025-06-01 22:06:27\')')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar bareng DIKA\', \'Seminar bareng DIKA\', \'2025-06-01T22:06\', \'2025-06-01T22:30\', \'Bandung\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748790387_98880db0aa38e02ab614.png\', \'signatures/1748790387_2b61c59d27719d810206.png\', \'2025-06-01 22:06:27\', \'2025-06-01 22:06:27\')')
 4 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 5 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 6 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 7 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 8 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
15 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
ERROR - 2025-06-01 22:07:17 --> mysqli_sql_exception: Can't DROP FOREIGN KEY `user_id`; check that it exists in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('ALTER TABLE `pa...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('ALTER TABLE `pa...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('ALTER TABLE `pa...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\Forge.php(539): CodeIgniter\Database\BaseConnection->query('ALTER TABLE `pa...')
#4 C:\sertifikat\myapp\app\Database\Migrations\2025-05-31-120000_AddUserIdToParticipantsTable.php(24): CodeIgniter\Database\Forge->dropForeignKey('participants', 'user_id')
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(876): App\Database\Migrations\AddUserIdToParticipantsTable->down()
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(289): CodeIgniter\Database\MigrationRunner->migrate('down', Object(stdClass))
#7 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Commands\Database\MigrateRollback.php(101): CodeIgniter\Database\MigrationRunner->regress(0)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Commands.php(74): CodeIgniter\Commands\Database\MigrateRollback->run(Array)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\BaseCommand.php(119): CodeIgniter\CLI\Commands->run('migrate:rollbac...', Array)
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Commands\Database\MigrateRefresh.php(86): CodeIgniter\CLI\BaseCommand->call('migrate:rollbac...', Array)
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Commands.php(74): CodeIgniter\Commands\Database\MigrateRefresh->run(Array)
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Console.php(47): CodeIgniter\CLI\Commands->run('migrate:refresh', Array)
#13 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(360): CodeIgniter\CLI\Console->run()
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#15 C:\sertifikat\myapp\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#16 {main}
ERROR - 2025-06-01 22:07:17 --> mysqli_sql_exception: Table 'participants' already exists in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('CREATE TABLE `p...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('CREATE TABLE `p...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('CREATE TABLE `p...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\Forge.php(570): CodeIgniter\Database\BaseConnection->query('CREATE TABLE `p...')
#4 C:\sertifikat\myapp\app\Database\Migrations\2025-06-01-081000_CreateParticipantsTable.php(53): CodeIgniter\Database\Forge->createTable('participants')
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(876): App\Database\Migrations\CreateParticipantsTable->up()
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(182): CodeIgniter\Database\MigrationRunner->migrate('up', Object(stdClass))
#7 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Commands\Database\Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest(NULL)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Commands.php(74): CodeIgniter\Commands\Database\Migrate->run(Array)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\BaseCommand.php(119): CodeIgniter\CLI\Commands->run('migrate', Array)
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Commands\Database\MigrateRefresh.php(87): CodeIgniter\CLI\BaseCommand->call('migrate', Array)
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Commands.php(74): CodeIgniter\Commands\Database\MigrateRefresh->run(Array)
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CLI\Console.php(47): CodeIgniter\CLI\Commands->run('migrate:refresh', Array)
#13 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(360): CodeIgniter\CLI\Console->run()
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#15 C:\sertifikat\myapp\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#16 {main}
DEBUG - 2025-06-01 22:07:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-01 22:07:51 --> mysqli_sql_exception: Unknown column 'signer1_name' in 'field list' in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `ev...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `ev...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `ev...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `ev...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\BaseModel.php(843): CodeIgniter\Model->doInsert(Array)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(800): CodeIgniter\BaseModel->insert(Array, true)
#7 C:\sertifikat\myapp\app\Controllers\Events.php(140): CodeIgniter\Model->insert(Array)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Events->store()
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#15 {main}
CRITICAL - 2025-06-01 22:07:51 --> CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'signer1_name' in 'field list'
[Method: POST, Route: events/store]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 2 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 3 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 4 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 5 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-06-01 22:07:51 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'signer1_name' in 'field list'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar bareng DIKA\', \'Seminar bareng DIKA\', \'2025-06-01T22:06\', \'2025-06-01T22:30\', \'Bandung\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748790471_194ff29fab38a79dc1b7.png\', \'signatures/1748790471_6b5e6c5d39261c23ef51.png\', \'2025-06-01 22:07:51\', \'2025-06-01 22:07:51\')')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar bareng DIKA\', \'Seminar bareng DIKA\', \'2025-06-01T22:06\', \'2025-06-01T22:30\', \'Bandung\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748790471_194ff29fab38a79dc1b7.png\', \'signatures/1748790471_6b5e6c5d39261c23ef51.png\', \'2025-06-01 22:07:51\', \'2025-06-01 22:07:51\')')
 3 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 4 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 5 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 6 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 7 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-06-01 22:07:51 --> [Caused by] mysqli_sql_exception: Unknown column 'signer1_name' in 'field list'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar bareng DIKA\', \'Seminar bareng DIKA\', \'2025-06-01T22:06\', \'2025-06-01T22:30\', \'Bandung\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748790471_194ff29fab38a79dc1b7.png\', \'signatures/1748790471_6b5e6c5d39261c23ef51.png\', \'2025-06-01 22:07:51\', \'2025-06-01 22:07:51\')', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar bareng DIKA\', \'Seminar bareng DIKA\', \'2025-06-01T22:06\', \'2025-06-01T22:30\', \'Bandung\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748790471_194ff29fab38a79dc1b7.png\', \'signatures/1748790471_6b5e6c5d39261c23ef51.png\', \'2025-06-01 22:07:51\', \'2025-06-01 22:07:51\')')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (\'Seminar bareng DIKA\', \'Seminar bareng DIKA\', \'2025-06-01T22:06\', \'2025-06-01T22:30\', \'Bandung\', \'10\', \'Andika Setiawan\', \'Narasumber\', \'Faiz Maulana\', \'Ketua Pelaksana\', \'signatures/1748790471_194ff29fab38a79dc1b7.png\', \'signatures/1748790471_6b5e6c5d39261c23ef51.png\', \'2025-06-01 22:07:51\', \'2025-06-01 22:07:51\')')
 4 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `events` (`title`, `description`, `start_date`, `end_date`, `location`, `max_participants`, `signer1_name`, `signer1_role`, `signer2_name`, `signer2_role`, `signer1_signature_path`, `signer2_signature_path`, `created_at`, `updated_at`) VALUES (:title:, :description:, :start_date:, :end_date:, :location:, :max_participants:, :signer1_name:, :signer1_role:, :signer2_name:, :signer2_role:, :signer1_signature_path:, :signer2_signature_path:, :created_at:, :updated_at:)', [...], false)
 5 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 6 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 7 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 8 APPPATH\Controllers\Events.php(140): CodeIgniter\Model->insert([...])
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
15 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-06-01 22:10:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:10:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:10:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:10:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:12:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:12:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:13:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:13:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:13:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:13:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:13:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:13:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:13:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:13:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:13:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:13:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:13:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:14:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:14:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:14:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:14:07 --> Current Time for Certificate Query: 2025-06-01 22:14:07
DEBUG - 2025-06-01 22:14:07 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '7'
DEBUG - 2025-06-01 22:14:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:14:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:14:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:14:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:14:12 --> User ID: 7
DEBUG - 2025-06-01 22:14:12 --> Event ID: 9
DEBUG - 2025-06-01 22:14:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:14:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:14:16 --> Current Time for Certificate Query: 2025-06-01 22:14:16
DEBUG - 2025-06-01 22:14:16 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '7'
DEBUG - 2025-06-01 22:14:16 --> Event: Seminar bareng DIKA, EndDate: 2025-06-01 22:30:00, IsCompleted: false
DEBUG - 2025-06-01 22:14:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:15:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:15:07 --> Current Time for Certificate Query: 2025-06-01 22:15:07
DEBUG - 2025-06-01 22:15:07 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '7'
DEBUG - 2025-06-01 22:15:07 --> Event: Seminar bareng DIKA, EndDate: 2025-06-01 22:30:00, IsCompleted: false
DEBUG - 2025-06-01 22:15:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:15:16 --> Current Time for Certificate Query: 2025-06-01 22:15:16
DEBUG - 2025-06-01 22:15:16 --> SQL Query for All Registered Events: SELECT `events`.*, `participants`.`created_at` as `registered_at`
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '7'
DEBUG - 2025-06-01 22:15:16 --> Event: Seminar bareng DIKA, EndDate: 2025-06-01 22:30:00, IsCompleted: false
DEBUG - 2025-06-01 22:15:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:15:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:16:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:16:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:16:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:16:28 --> SQL Query: 
DEBUG - 2025-06-01 22:16:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:16:31 --> SQL Query: 
DEBUG - 2025-06-01 22:18:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:18:23 --> SQL Query: 
DEBUG - 2025-06-01 22:18:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:18:26 --> SQL Query: 
DEBUG - 2025-06-01 22:18:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:18:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:18:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:18:41 --> SQL Query: 
DEBUG - 2025-06-01 22:18:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:18:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:18:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:19:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:19:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:19:23 --> SQL Query: 
DEBUG - 2025-06-01 22:20:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:20:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:20:25 --> SQL Query: 
DEBUG - 2025-06-01 22:20:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:20:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:20:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:20:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:21:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:21:13 --> SQL Query: 
DEBUG - 2025-06-01 22:21:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:21:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:21:18 --> SQL Query: 
DEBUG - 2025-06-01 22:22:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:22:12 --> SQL Query: 
DEBUG - 2025-06-01 22:22:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:24:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:24:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:24:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:24:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:24:20 --> Current Time for Certificate Query: 2025-06-01 22:24:20
DEBUG - 2025-06-01 22:24:20 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '7'
DEBUG - 2025-06-01 22:24:20 --> Event: Seminar bareng DIKA, EndDate: 2025-06-01 22:30:00, IsCompleted: false
DEBUG - 2025-06-01 22:32:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:32:17 --> Current Time for Certificate Query: 2025-06-01 22:32:17
DEBUG - 2025-06-01 22:32:17 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '7'
DEBUG - 2025-06-01 22:32:17 --> Event: Seminar bareng DIKA, EndDate: 2025-06-01 22:30:00, IsCompleted: true
DEBUG - 2025-06-01 22:32:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:32:21 --> User ID: 7
DEBUG - 2025-06-01 22:32:21 --> Event ID: 9
DEBUG - 2025-06-01 22:32:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:32:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:32:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-01 22:32:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
