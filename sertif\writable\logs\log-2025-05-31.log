DEBUG - 2025-05-31 09:31:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:31:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:31:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:31:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:31:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:31:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-31 09:33:06 --> ArgumentCountError: Too few arguments to function App\Controllers\Events::register(), 0 passed in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php on line 933 and exactly 1 expected
[Method: GET, Route: events/register]
in APPPATH\Controllers\Events.php on line 125.
 1 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register()
 2 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 09:33:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-31 09:33:40 --> ErrorException: Undefined variable $event
[Method: GET, Route: events/register]
in APPPATH\Views\pages\events\register.php on line 2.
 1 APPPATH\Views\pages\events\register.php(2): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined variable $event', 'C:\\sertifikat\\myapp\\app\\Views\\pages\\events\\register.php', 2)
 2 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\myapp\\app\\Views\\pages\\events\\register.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/events/register', [], true)
 5 APPPATH\Views\layout\main.php(136): view('pages/events/register')
 6 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\myapp\\app\\Views\\layout\\main.php')
 7 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 8 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('layout/main', [], true)
 9 APPPATH\Controllers\Events.php(137): view('layout/main', [...])
10 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register()
11 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
12 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
13 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
15 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
16 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 09:34:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-31 09:34:09 --> ErrorException: Undefined variable $event
[Method: GET, Route: events/register]
in APPPATH\Views\pages\events\register.php on line 2.
 1 APPPATH\Views\pages\events\register.php(2): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined variable $event', 'C:\\sertifikat\\myapp\\app\\Views\\pages\\events\\register.php', 2)
 2 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\myapp\\app\\Views\\pages\\events\\register.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/events/register', [], true)
 5 APPPATH\Views\layout\main.php(136): view('pages/events/register')
 6 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\myapp\\app\\Views\\layout\\main.php')
 7 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 8 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('layout/main', [], true)
 9 APPPATH\Controllers\Events.php(141): view('layout/main', [...])
10 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register()
11 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
12 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
13 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
15 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
16 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 09:34:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:34:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-05-31 09:34:23 --> mysqli_sql_exception: Column 'name' cannot be null in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `pa...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `pa...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `pa...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `pa...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\BaseModel.php(843): CodeIgniter\Model->doInsert(Array)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(800): CodeIgniter\BaseModel->insert(Array, true)
#7 C:\sertifikat\myapp\app\Controllers\Events.php(176): CodeIgniter\Model->insert(Array)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Events->register(1)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#15 {main}
CRITICAL - 2025-05-31 09:34:23 --> CodeIgniter\Database\Exceptions\DatabaseException: Column 'name' cannot be null
[Method: GET, Route: events/register/1]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 2 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 3 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 4 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 5 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 09:34:23 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Column 'name' cannot be null
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, \'<EMAIL>\', NULL, \'2025-05-31 09:34:23\', \'2025-05-31 09:34:23\')')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, \'<EMAIL>\', NULL, \'2025-05-31 09:34:23\', \'2025-05-31 09:34:23\')')
 3 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 4 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 5 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 6 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 7 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 09:34:23 --> [Caused by] mysqli_sql_exception: Column 'name' cannot be null
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, \'<EMAIL>\', NULL, \'2025-05-31 09:34:23\', \'2025-05-31 09:34:23\')', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, \'<EMAIL>\', NULL, \'2025-05-31 09:34:23\', \'2025-05-31 09:34:23\')')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, \'<EMAIL>\', NULL, \'2025-05-31 09:34:23\', \'2025-05-31 09:34:23\')')
 4 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 5 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 6 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 7 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 8 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
15 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 09:34:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:34:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-05-31 09:34:30 --> mysqli_sql_exception: Column 'name' cannot be null in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `pa...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `pa...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `pa...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `pa...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\BaseModel.php(843): CodeIgniter\Model->doInsert(Array)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(800): CodeIgniter\BaseModel->insert(Array, true)
#7 C:\sertifikat\myapp\app\Controllers\Events.php(176): CodeIgniter\Model->insert(Array)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Events->register(1)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#15 {main}
CRITICAL - 2025-05-31 09:34:30 --> CodeIgniter\Database\Exceptions\DatabaseException: Column 'name' cannot be null
[Method: GET, Route: events/register/1]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 2 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 3 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 4 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 5 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 09:34:30 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Column 'name' cannot be null
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, \'<EMAIL>\', NULL, \'2025-05-31 09:34:30\', \'2025-05-31 09:34:30\')')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, \'<EMAIL>\', NULL, \'2025-05-31 09:34:30\', \'2025-05-31 09:34:30\')')
 3 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 4 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 5 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 6 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 7 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 09:34:30 --> [Caused by] mysqli_sql_exception: Column 'name' cannot be null
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, \'<EMAIL>\', NULL, \'2025-05-31 09:34:30\', \'2025-05-31 09:34:30\')', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, \'<EMAIL>\', NULL, \'2025-05-31 09:34:30\', \'2025-05-31 09:34:30\')')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, \'<EMAIL>\', NULL, \'2025-05-31 09:34:30\', \'2025-05-31 09:34:30\')')
 4 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 5 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 6 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 7 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 8 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
15 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 09:35:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-05-31 09:35:28 --> mysqli_sql_exception: Column 'name' cannot be null in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `pa...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `pa...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `pa...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `pa...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\BaseModel.php(843): CodeIgniter\Model->doInsert(Array)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(800): CodeIgniter\BaseModel->insert(Array, true)
#7 C:\sertifikat\myapp\app\Controllers\Events.php(176): CodeIgniter\Model->insert(Array)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Events->register(1)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#15 {main}
CRITICAL - 2025-05-31 09:35:28 --> CodeIgniter\Database\Exceptions\DatabaseException: Column 'name' cannot be null
[Method: GET, Route: events/register/1]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 2 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 3 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 4 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 5 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 09:35:28 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Column 'name' cannot be null
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:35:28\', \'2025-05-31 09:35:28\')')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:35:28\', \'2025-05-31 09:35:28\')')
 3 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 4 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 5 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 6 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 7 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 09:35:28 --> [Caused by] mysqli_sql_exception: Column 'name' cannot be null
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:35:28\', \'2025-05-31 09:35:28\')', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:35:28\', \'2025-05-31 09:35:28\')')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:35:28\', \'2025-05-31 09:35:28\')')
 4 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 5 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 6 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 7 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 8 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
15 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 09:35:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-05-31 09:35:33 --> mysqli_sql_exception: Column 'name' cannot be null in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `pa...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `pa...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `pa...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `pa...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\BaseModel.php(843): CodeIgniter\Model->doInsert(Array)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(800): CodeIgniter\BaseModel->insert(Array, true)
#7 C:\sertifikat\myapp\app\Controllers\Events.php(176): CodeIgniter\Model->insert(Array)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Events->register(1)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#15 {main}
CRITICAL - 2025-05-31 09:35:33 --> CodeIgniter\Database\Exceptions\DatabaseException: Column 'name' cannot be null
[Method: GET, Route: events/register/1]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 2 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 3 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 4 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 5 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 09:35:33 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Column 'name' cannot be null
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:35:33\', \'2025-05-31 09:35:33\')')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:35:33\', \'2025-05-31 09:35:33\')')
 3 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 4 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 5 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 6 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 7 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 09:35:33 --> [Caused by] mysqli_sql_exception: Column 'name' cannot be null
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:35:33\', \'2025-05-31 09:35:33\')', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:35:33\', \'2025-05-31 09:35:33\')')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:35:33\', \'2025-05-31 09:35:33\')')
 4 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 5 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 6 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 7 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 8 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
15 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 09:35:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:35:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-05-31 09:35:38 --> mysqli_sql_exception: Column 'name' cannot be null in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `pa...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `pa...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `pa...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `pa...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\BaseModel.php(843): CodeIgniter\Model->doInsert(Array)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(800): CodeIgniter\BaseModel->insert(Array, true)
#7 C:\sertifikat\myapp\app\Controllers\Events.php(176): CodeIgniter\Model->insert(Array)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Events->register(1)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#15 {main}
CRITICAL - 2025-05-31 09:35:38 --> CodeIgniter\Database\Exceptions\DatabaseException: Column 'name' cannot be null
[Method: GET, Route: events/register/1]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 2 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 3 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 4 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 5 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 09:35:38 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Column 'name' cannot be null
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:35:38\', \'2025-05-31 09:35:38\')')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:35:38\', \'2025-05-31 09:35:38\')')
 3 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 4 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 5 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 6 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 7 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 09:35:38 --> [Caused by] mysqli_sql_exception: Column 'name' cannot be null
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:35:38\', \'2025-05-31 09:35:38\')', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:35:38\', \'2025-05-31 09:35:38\')')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:35:38\', \'2025-05-31 09:35:38\')')
 4 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 5 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 6 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 7 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 8 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
15 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 09:35:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:35:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:35:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:35:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:35:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:36:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:36:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:36:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:36:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:36:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:36:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:36:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:36:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:36:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:36:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:36:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:37:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:37:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:38:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:38:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:38:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:38:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:39:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:39:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:39:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-05-31 09:39:03 --> mysqli_sql_exception: Column 'name' cannot be null in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `pa...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `pa...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `pa...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `pa...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\BaseModel.php(843): CodeIgniter\Model->doInsert(Array)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(800): CodeIgniter\BaseModel->insert(Array, true)
#7 C:\sertifikat\myapp\app\Controllers\Events.php(176): CodeIgniter\Model->insert(Array)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Events->register(1)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#15 {main}
CRITICAL - 2025-05-31 09:39:03 --> CodeIgniter\Database\Exceptions\DatabaseException: Column 'name' cannot be null
[Method: GET, Route: events/register/1]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 2 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 3 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 4 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 5 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 09:39:03 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Column 'name' cannot be null
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:39:03\', \'2025-05-31 09:39:03\')')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:39:03\', \'2025-05-31 09:39:03\')')
 3 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 4 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 5 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 6 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 7 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 09:39:03 --> [Caused by] mysqli_sql_exception: Column 'name' cannot be null
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:39:03\', \'2025-05-31 09:39:03\')', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:39:03\', \'2025-05-31 09:39:03\')')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:39:03\', \'2025-05-31 09:39:03\')')
 4 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 5 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 6 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 7 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 8 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
15 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 09:53:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-05-31 09:53:41 --> mysqli_sql_exception: Column 'name' cannot be null in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `pa...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `pa...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `pa...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `pa...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\BaseModel.php(843): CodeIgniter\Model->doInsert(Array)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(800): CodeIgniter\BaseModel->insert(Array, true)
#7 C:\sertifikat\myapp\app\Controllers\Events.php(176): CodeIgniter\Model->insert(Array)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Events->register(1)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#15 {main}
CRITICAL - 2025-05-31 09:53:41 --> CodeIgniter\Database\Exceptions\DatabaseException: Column 'name' cannot be null
[Method: GET, Route: events/register/1]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 2 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 3 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 4 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 5 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 09:53:41 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Column 'name' cannot be null
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:53:41\', \'2025-05-31 09:53:41\')')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:53:41\', \'2025-05-31 09:53:41\')')
 3 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 4 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 5 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 6 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 7 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 09:53:41 --> [Caused by] mysqli_sql_exception: Column 'name' cannot be null
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:53:41\', \'2025-05-31 09:53:41\')', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:53:41\', \'2025-05-31 09:53:41\')')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:53:41\', \'2025-05-31 09:53:41\')')
 4 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 5 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 6 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 7 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 8 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
15 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 09:53:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:53:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:54:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:54:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-05-31 09:54:31 --> mysqli_sql_exception: Column 'name' cannot be null in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `pa...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `pa...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `pa...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `pa...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\BaseModel.php(843): CodeIgniter\Model->doInsert(Array)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(800): CodeIgniter\BaseModel->insert(Array, true)
#7 C:\sertifikat\myapp\app\Controllers\Events.php(176): CodeIgniter\Model->insert(Array)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Events->register(1)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#15 {main}
CRITICAL - 2025-05-31 09:54:31 --> CodeIgniter\Database\Exceptions\DatabaseException: Column 'name' cannot be null
[Method: GET, Route: events/register/1]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 2 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 3 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 4 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 5 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 09:54:31 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Column 'name' cannot be null
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:54:31\', \'2025-05-31 09:54:31\')')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:54:31\', \'2025-05-31 09:54:31\')')
 3 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 4 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 5 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 6 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 7 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 09:54:31 --> [Caused by] mysqli_sql_exception: Column 'name' cannot be null
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:54:31\', \'2025-05-31 09:54:31\')', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:54:31\', \'2025-05-31 09:54:31\')')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:54:31\', \'2025-05-31 09:54:31\')')
 4 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 5 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 6 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 7 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 8 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
15 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 09:55:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:55:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-31 09:55:04 --> ErrorException: Undefined variable $userName
[Method: GET, Route: /]
in APPPATH\Views\pages\participant_dashboard.php on line 3.
 1 APPPATH\Views\pages\participant_dashboard.php(3): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined variable $userName', 'C:\\sertifikat\\myapp\\app\\Views\\pages\\participant_dashboard.php', 3)
 2 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\myapp\\app\\Views\\pages\\participant_dashboard.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/participant_dashboard', [], true)
 5 APPPATH\Views\layout\main.php(136): view('pages/participant_dashboard')
 6 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\myapp\\app\\Views\\layout\\main.php')
 7 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 8 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('layout/main', [], true)
 9 APPPATH\Controllers\Home.php(37): view('layout/main', [...])
10 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Home->index()
11 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Home))
12 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
13 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
15 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
16 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 09:56:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:56:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:56:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-05-31 09:56:39 --> mysqli_sql_exception: Column 'name' cannot be null in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `pa...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `pa...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `pa...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `pa...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\BaseModel.php(843): CodeIgniter\Model->doInsert(Array)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(800): CodeIgniter\BaseModel->insert(Array, true)
#7 C:\sertifikat\myapp\app\Controllers\Events.php(176): CodeIgniter\Model->insert(Array)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Events->register(1)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#15 {main}
CRITICAL - 2025-05-31 09:56:39 --> CodeIgniter\Database\Exceptions\DatabaseException: Column 'name' cannot be null
[Method: GET, Route: events/register/1]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 2 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 3 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 4 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 5 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 09:56:39 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Column 'name' cannot be null
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:56:39\', \'2025-05-31 09:56:39\')')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:56:39\', \'2025-05-31 09:56:39\')')
 3 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 4 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 5 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 6 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 7 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 09:56:39 --> [Caused by] mysqli_sql_exception: Column 'name' cannot be null
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:56:39\', \'2025-05-31 09:56:39\')', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:56:39\', \'2025-05-31 09:56:39\')')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 09:56:39\', \'2025-05-31 09:56:39\')')
 4 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 5 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 6 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 7 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 8 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
15 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 09:56:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 09:57:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 10:02:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 10:32:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 10:32:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 10:33:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 10:33:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 10:33:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 10:33:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 10:33:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 10:33:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-05-31 10:33:26 --> mysqli_sql_exception: Column 'name' cannot be null in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `pa...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `pa...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `pa...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `pa...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\BaseModel.php(843): CodeIgniter\Model->doInsert(Array)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(800): CodeIgniter\BaseModel->insert(Array, true)
#7 C:\sertifikat\myapp\app\Controllers\Events.php(176): CodeIgniter\Model->insert(Array)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Events->register(1)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#15 {main}
CRITICAL - 2025-05-31 10:33:26 --> CodeIgniter\Database\Exceptions\DatabaseException: Column 'name' cannot be null
[Method: GET, Route: events/register/1]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 2 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 3 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 4 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 5 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 10:33:26 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Column 'name' cannot be null
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 10:33:26\', \'2025-05-31 10:33:26\')')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 10:33:26\', \'2025-05-31 10:33:26\')')
 3 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 4 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 5 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 6 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 7 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 10:33:26 --> [Caused by] mysqli_sql_exception: Column 'name' cannot be null
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 10:33:26\', \'2025-05-31 10:33:26\')', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 10:33:26\', \'2025-05-31 10:33:26\')')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 10:33:26\', \'2025-05-31 10:33:26\')')
 4 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 5 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 6 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 7 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 8 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
15 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 10:35:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-05-31 10:35:59 --> mysqli_sql_exception: Column 'name' cannot be null in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `pa...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `pa...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `pa...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `pa...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\BaseModel.php(843): CodeIgniter\Model->doInsert(Array)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(800): CodeIgniter\BaseModel->insert(Array, true)
#7 C:\sertifikat\myapp\app\Controllers\Events.php(176): CodeIgniter\Model->insert(Array)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Events->register(1)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#15 {main}
CRITICAL - 2025-05-31 10:35:59 --> CodeIgniter\Database\Exceptions\DatabaseException: Column 'name' cannot be null
[Method: GET, Route: events/register/1]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 2 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 3 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 4 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 5 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 10:35:59 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Column 'name' cannot be null
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 10:35:59\', \'2025-05-31 10:35:59\')')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 10:35:59\', \'2025-05-31 10:35:59\')')
 3 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 4 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 5 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 6 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 7 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 10:35:59 --> [Caused by] mysqli_sql_exception: Column 'name' cannot be null
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 10:35:59\', \'2025-05-31 10:35:59\')', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 10:35:59\', \'2025-05-31 10:35:59\')')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 10:35:59\', \'2025-05-31 10:35:59\')')
 4 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 5 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 6 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 7 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 8 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
15 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 11:00:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-05-31 11:00:25 --> mysqli_sql_exception: Column 'name' cannot be null in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `pa...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `pa...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `pa...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `pa...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
#5 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\BaseModel.php(843): CodeIgniter\Model->doInsert(Array)
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(800): CodeIgniter\BaseModel->insert(Array, true)
#7 C:\sertifikat\myapp\app\Controllers\Events.php(176): CodeIgniter\Model->insert(Array)
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Events->register(1)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#15 {main}
CRITICAL - 2025-05-31 11:00:25 --> CodeIgniter\Database\Exceptions\DatabaseException: Column 'name' cannot be null
[Method: GET, Route: events/register/1]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 2 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 3 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 4 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 5 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 11:00:25 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Column 'name' cannot be null
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 11:00:25\', \'2025-05-31 11:00:25\')')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 11:00:25\', \'2025-05-31 11:00:25\')')
 3 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 4 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 5 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 6 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 7 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 11:00:25 --> [Caused by] mysqli_sql_exception: Column 'name' cannot be null
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 11:00:25\', \'2025-05-31 11:00:25\')', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 11:00:25\', \'2025-05-31 11:00:25\')')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (1, NULL, NULL, NULL, \'2025-05-31 11:00:25\', \'2025-05-31 11:00:25\')')
 4 SYSTEMPATH\Database\BaseBuilder.php(2345): CodeIgniter\Database\BaseConnection->query('INSERT INTO `participants` (`event_id`, `name`, `email`, `phone`, `created_at`, `updated_at`) VALUES (:event_id:, :name:, :email:, :phone:, :created_at:, :updated_at:)', [...], false)
 5 SYSTEMPATH\Model.php(394): CodeIgniter\Database\BaseBuilder->insert()
 6 SYSTEMPATH\BaseModel.php(843): CodeIgniter\Model->doInsert([...])
 7 SYSTEMPATH\Model.php(800): CodeIgniter\BaseModel->insert([...], true)
 8 APPPATH\Controllers\Events.php(176): CodeIgniter\Model->insert([...])
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->register(1)
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
15 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 11:02:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:02:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:02:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:02:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:02:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:02:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:02:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:02:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:02:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:02:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:02:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:02:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:02:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:02:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:02:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:03:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:03:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:04:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:04:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:04:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:04:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:04:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:04:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:04:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:04:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:04:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:04:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:04:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-31 11:04:57 --> ErrorException: foreach() argument must be of type array|object, int given
[Method: GET, Route: /]
in APPPATH\Views\pages\participant_dashboard.php on line 12.
 1 APPPATH\Views\pages\participant_dashboard.php(12): CodeIgniter\Debug\Exceptions->errorHandler(2, 'foreach() argument must be of type array|object, int given', 'C:\\sertifikat\\myapp\\app\\Views\\pages\\participant_dashboard.php', 12)
 2 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\myapp\\app\\Views\\pages\\participant_dashboard.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/participant_dashboard', [], true)
 5 APPPATH\Views\layout\main.php(136): view('pages/participant_dashboard')
 6 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\myapp\\app\\Views\\layout\\main.php')
 7 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 8 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('layout/main', [], true)
 9 APPPATH\Controllers\Home.php(37): view('layout/main', [...])
10 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Home->index()
11 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Home))
12 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
13 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
15 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
16 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 11:04:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-31 11:04:59 --> ErrorException: foreach() argument must be of type array|object, int given
[Method: GET, Route: /]
in APPPATH\Views\pages\participant_dashboard.php on line 12.
 1 APPPATH\Views\pages\participant_dashboard.php(12): CodeIgniter\Debug\Exceptions->errorHandler(2, 'foreach() argument must be of type array|object, int given', 'C:\\sertifikat\\myapp\\app\\Views\\pages\\participant_dashboard.php', 12)
 2 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\myapp\\app\\Views\\pages\\participant_dashboard.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/participant_dashboard', [], true)
 5 APPPATH\Views\layout\main.php(136): view('pages/participant_dashboard')
 6 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\myapp\\app\\Views\\layout\\main.php')
 7 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 8 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('layout/main', [], true)
 9 APPPATH\Controllers\Home.php(37): view('layout/main', [...])
10 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Home->index()
11 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Home))
12 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
13 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
15 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
16 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 11:06:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:06:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:06:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-31 11:06:09 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "pages/events/view.php"
[Method: GET, Route: events/view/1]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('pages/events/view.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/events/view', [], true)
 3 APPPATH\Views\layout\main.php(136): view('pages/events/view')
 4 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\myapp\\app\\Views\\layout\\main.php')
 5 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 6 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('layout/main', [], true)
 7 APPPATH\Controllers\Events.php(108): view('layout/main', [...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->view(1)
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 11:09:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-31 11:09:33 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "pages/events/view.php"
[Method: GET, Route: events/view/1]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('pages/events/view.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/events/view', [], true)
 3 APPPATH\Views\layout\main.php(136): view('pages/events/view')
 4 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\myapp\\app\\Views\\layout\\main.php')
 5 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 6 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('layout/main', [], true)
 7 APPPATH\Controllers\Events.php(115): view('layout/main', [...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->view(1)
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 11:10:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:10:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:10:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:10:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:10:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:10:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:10:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:10:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:10:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:10:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:10:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:10:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:10:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:10:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:11:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:11:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:11:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:11:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:11:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:11:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:11:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:11:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:11:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:11:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:11:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:20:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:20:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:20:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-31 11:20:28 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "pages/events/edit.php"
[Method: GET, Route: events/edit/1]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('pages/events/edit.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/events/edit', [], true)
 3 APPPATH\Views\layout\main.php(136): view('pages/events/edit')
 4 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\myapp\\app\\Views\\layout\\main.php')
 5 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 6 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('layout/main', [], true)
 7 APPPATH\Controllers\Events.php(83): view('layout/main', [...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->edit(1)
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 11:20:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-31 11:20:47 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "pages/events/edit.php"
[Method: GET, Route: events/edit/1]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('pages/events/edit.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/events/edit', [], true)
 3 APPPATH\Views\layout\main.php(136): view('pages/events/edit')
 4 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\myapp\\app\\Views\\layout\\main.php')
 5 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 6 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('layout/main', [], true)
 7 APPPATH\Controllers\Events.php(83): view('layout/main', [...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->edit(1)
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 11:23:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:35:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:35:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:35:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:35:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:35:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:35:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:35:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-31 11:35:34 --> CodeIgniter\Validation\Exceptions\ValidationException: "after_than" is not a valid rule.
[Method: POST, Route: events/update/1]
in SYSTEMPATH\Validation\Validation.php on line 345.
 1 SYSTEMPATH\Validation\Validation.php(345): CodeIgniter\Validation\Exceptions\ValidationException::forRuleNotFound('after_than')
 2 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('end_date', 'end_date', '2025-05-28T18:42', [...], [...], 'end_date')
 3 SYSTEMPATH\Controller.php(138): CodeIgniter\Validation\Validation->run()
 4 APPPATH\Controllers\Events.php(135): CodeIgniter\Controller->validate([...])
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->update(1)
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 11:42:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-31 11:42:15 --> CodeIgniter\Validation\Exceptions\ValidationException: "after_than" is not a valid rule.
[Method: POST, Route: events/update/1]
in SYSTEMPATH\Validation\Validation.php on line 345.
 1 SYSTEMPATH\Validation\Validation.php(345): CodeIgniter\Validation\Exceptions\ValidationException::forRuleNotFound('after_than')
 2 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('end_date', 'end_date', '2025-05-28T18:42', [...], [...], 'end_date')
 3 SYSTEMPATH\Controller.php(138): CodeIgniter\Validation\Validation->run()
 4 APPPATH\Controllers\Events.php(135): CodeIgniter\Controller->validate([...])
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->update(1)
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 11:42:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 11:42:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-31 11:42:21 --> CodeIgniter\Validation\Exceptions\ValidationException: "after_than" is not a valid rule.
[Method: POST, Route: events/update/1]
in SYSTEMPATH\Validation\Validation.php on line 345.
 1 SYSTEMPATH\Validation\Validation.php(345): CodeIgniter\Validation\Exceptions\ValidationException::forRuleNotFound('after_than')
 2 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('end_date', 'end_date', '2025-05-28T18:42', [...], [...], 'end_date')
 3 SYSTEMPATH\Controller.php(138): CodeIgniter\Validation\Validation->run()
 4 APPPATH\Controllers\Events.php(135): CodeIgniter\Controller->validate([...])
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->update(1)
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 12:00:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-31 12:00:30 --> CodeIgniter\Validation\Exceptions\ValidationException: "after_than" is not a valid rule.
[Method: POST, Route: events/update/1]
in SYSTEMPATH\Validation\Validation.php on line 345.
 1 SYSTEMPATH\Validation\Validation.php(345): CodeIgniter\Validation\Exceptions\ValidationException::forRuleNotFound('after_than')
 2 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('end_date', 'end_date', '2025-05-28T18:42', [...], [...], 'end_date')
 3 SYSTEMPATH\Controller.php(138): CodeIgniter\Validation\Validation->run()
 4 APPPATH\Controllers\Events.php(135): CodeIgniter\Controller->validate([...])
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->update(1)
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 12:02:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-31 12:02:59 --> CodeIgniter\Validation\Exceptions\ValidationException: "after_than" is not a valid rule.
[Method: POST, Route: events/update/1]
in SYSTEMPATH\Validation\Validation.php on line 345.
 1 SYSTEMPATH\Validation\Validation.php(345): CodeIgniter\Validation\Exceptions\ValidationException::forRuleNotFound('after_than')
 2 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('end_date', 'end_date', '2025-05-28T18:42', [...], [...], 'end_date')
 3 SYSTEMPATH\Controller.php(138): CodeIgniter\Validation\Validation->run()
 4 APPPATH\Controllers\Events.php(135): CodeIgniter\Controller->validate([...])
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->update(1)
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 12:03:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:03:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-31 12:03:04 --> CodeIgniter\Validation\Exceptions\ValidationException: "after_than" is not a valid rule.
[Method: POST, Route: events/update/1]
in SYSTEMPATH\Validation\Validation.php on line 345.
 1 SYSTEMPATH\Validation\Validation.php(345): CodeIgniter\Validation\Exceptions\ValidationException::forRuleNotFound('after_than')
 2 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('end_date', 'end_date', '2025-05-28T18:42', [...], [...], 'end_date')
 3 SYSTEMPATH\Controller.php(138): CodeIgniter\Validation\Validation->run()
 4 APPPATH\Controllers\Events.php(135): CodeIgniter\Controller->validate([...])
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->update(1)
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 12:03:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:03:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:03:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:03:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:03:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:04:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-31 12:04:19 --> CodeIgniter\Validation\Exceptions\ValidationException: "after_field" is not a valid rule.
[Method: POST, Route: events/store]
in SYSTEMPATH\Validation\Validation.php on line 345.
 1 SYSTEMPATH\Validation\Validation.php(345): CodeIgniter\Validation\Exceptions\ValidationException::forRuleNotFound('after_field')
 2 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('end_date', 'end_date', '2025-05-31T23:03', [...], [...], 'end_date')
 3 SYSTEMPATH\Controller.php(138): CodeIgniter\Validation\Validation->run()
 4 APPPATH\Controllers\Events.php(82): CodeIgniter\Controller->validate([...])
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 12:11:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-31 12:11:20 --> CodeIgniter\Validation\Exceptions\ValidationException: "after" is not a valid rule.
[Method: POST, Route: events/store]
in SYSTEMPATH\Validation\Validation.php on line 345.
 1 SYSTEMPATH\Validation\Validation.php(345): CodeIgniter\Validation\Exceptions\ValidationException::forRuleNotFound('after')
 2 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('end_date', 'end_date', '2025-05-31T23:03', [...], [...], 'end_date')
 3 SYSTEMPATH\Controller.php(138): CodeIgniter\Validation\Validation->run()
 4 APPPATH\Controllers\Events.php(82): CodeIgniter\Controller->validate([...])
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 12:11:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:11:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:11:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:11:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:12:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:12:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:12:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:12:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:14:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-31 12:14:25 --> CodeIgniter\Validation\Exceptions\ValidationException: "validate_date_after" is not a valid rule.
[Method: POST, Route: events/store]
in SYSTEMPATH\Validation\Validation.php on line 345.
 1 SYSTEMPATH\Validation\Validation.php(345): CodeIgniter\Validation\Exceptions\ValidationException::forRuleNotFound('validate_date_after')
 2 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('end_date', 'end_date', '2025-06-01T23:03', [...], [...], 'end_date')
 3 SYSTEMPATH\Controller.php(138): CodeIgniter\Validation\Validation->run()
 4 APPPATH\Controllers\Events.php(82): CodeIgniter\Controller->validate([...])
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 12:14:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:14:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-31 12:14:37 --> CodeIgniter\Validation\Exceptions\ValidationException: "validate_date_after" is not a valid rule.
[Method: POST, Route: events/store]
in SYSTEMPATH\Validation\Validation.php on line 345.
 1 SYSTEMPATH\Validation\Validation.php(345): CodeIgniter\Validation\Exceptions\ValidationException::forRuleNotFound('validate_date_after')
 2 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('end_date', 'end_date', '2025-06-01T23:03', [...], [...], 'end_date')
 3 SYSTEMPATH\Controller.php(138): CodeIgniter\Validation\Validation->run()
 4 APPPATH\Controllers\Events.php(82): CodeIgniter\Controller->validate([...])
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 12:15:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-31 12:15:57 --> CodeIgniter\Validation\Exceptions\ValidationException: "validate_date_after" is not a valid rule.
[Method: POST, Route: events/store]
in SYSTEMPATH\Validation\Validation.php on line 345.
 1 SYSTEMPATH\Validation\Validation.php(345): CodeIgniter\Validation\Exceptions\ValidationException::forRuleNotFound('validate_date_after')
 2 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('end_date', 'end_date', '2025-06-01T23:03', [...], [...], 'end_date')
 3 APPPATH\Controllers\Events.php(89): CodeIgniter\Validation\Validation->run()
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
10 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 12:16:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-31 12:16:40 --> CodeIgniter\Validation\Exceptions\ValidationException: "callback_validate_date_after" is not a valid rule.
[Method: POST, Route: events/store]
in SYSTEMPATH\Validation\Validation.php on line 345.
 1 SYSTEMPATH\Validation\Validation.php(345): CodeIgniter\Validation\Exceptions\ValidationException::forRuleNotFound('callback_validate_date_after')
 2 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('end_date', 'end_date', '2025-06-01T23:03', [...], [...], 'end_date')
 3 APPPATH\Controllers\Events.php(104): CodeIgniter\Validation\Validation->run()
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
10 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 12:16:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:16:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-31 12:16:46 --> CodeIgniter\Validation\Exceptions\ValidationException: "callback_validate_date_after" is not a valid rule.
[Method: POST, Route: events/store]
in SYSTEMPATH\Validation\Validation.php on line 345.
 1 SYSTEMPATH\Validation\Validation.php(345): CodeIgniter\Validation\Exceptions\ValidationException::forRuleNotFound('callback_validate_date_after')
 2 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('end_date', 'end_date', '2025-06-01T23:03', [...], [...], 'end_date')
 3 APPPATH\Controllers\Events.php(104): CodeIgniter\Validation\Validation->run()
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
10 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 12:17:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-31 12:17:27 --> CodeIgniter\Validation\Exceptions\ValidationException: "callback_$validate_date_after" is not a valid rule.
[Method: POST, Route: events/store]
in SYSTEMPATH\Validation\Validation.php on line 345.
 1 SYSTEMPATH\Validation\Validation.php(345): CodeIgniter\Validation\Exceptions\ValidationException::forRuleNotFound('callback_$validate_date_after')
 2 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('end_date', 'end_date', '2025-06-01T23:03', [...], [...], 'end_date')
 3 APPPATH\Controllers\Events.php(104): CodeIgniter\Validation\Validation->run()
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->store()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
10 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 12:18:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:18:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:18:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:18:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:18:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:18:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-31 12:18:24 --> CodeIgniter\Validation\Exceptions\ValidationException: "after_than" is not a valid rule.
[Method: POST, Route: events/update/2]
in SYSTEMPATH\Validation\Validation.php on line 345.
 1 SYSTEMPATH\Validation\Validation.php(345): CodeIgniter\Validation\Exceptions\ValidationException::forRuleNotFound('after_than')
 2 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('end_date', 'end_date', '2025-05-28T18:42', [...], [...], 'end_date')
 3 SYSTEMPATH\Controller.php(138): CodeIgniter\Validation\Validation->run()
 4 APPPATH\Controllers\Events.php(146): CodeIgniter\Controller->validate([...])
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->update(2)
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 12:18:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:18:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:18:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:18:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-31 12:18:35 --> CodeIgniter\Validation\Exceptions\ValidationException: "after_than" is not a valid rule.
[Method: POST, Route: events/update/3]
in SYSTEMPATH\Validation\Validation.php on line 345.
 1 SYSTEMPATH\Validation\Validation.php(345): CodeIgniter\Validation\Exceptions\ValidationException::forRuleNotFound('after_than')
 2 SYSTEMPATH\Validation\Validation.php(201): CodeIgniter\Validation\Validation->processRules('end_date', 'end_date', '2025-06-01T23:03', [...], [...], 'end_date')
 3 SYSTEMPATH\Controller.php(138): CodeIgniter\Validation\Validation->run()
 4 APPPATH\Controllers\Events.php(146): CodeIgniter\Controller->validate([...])
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->update(3)
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 12:19:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:19:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:19:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:19:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:19:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:19:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:19:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:19:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:19:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:19:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:20:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:20:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:20:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:20:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:20:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:20:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:20:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:21:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:21:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:21:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:21:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:21:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:21:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:22:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:22:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:24:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-31 12:24:05 --> ErrorException: Undefined array key "title"
[Method: GET, Route: events/certificate]
in APPPATH\Views\pages\events\certificate.php on line 4.
 1 APPPATH\Views\pages\events\certificate.php(4): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "title"', 'C:\\sertifikat\\myapp\\app\\Views\\pages\\events\\certificate.php', 4)
 2 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\myapp\\app\\Views\\pages\\events\\certificate.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/events/certificate', [], true)
 5 APPPATH\Views\layout\main.php(136): view('pages/events/certificate')
 6 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\myapp\\app\\Views\\layout\\main.php')
 7 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 8 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('layout/main', [], true)
 9 APPPATH\Controllers\Events.php(276): view('layout/main', [...])
10 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->certificate()
11 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
12 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
13 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
15 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
16 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 12:25:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:25:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:25:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:25:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:27:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:28:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-05-31 12:28:03 --> mysqli_sql_exception: Unknown column 'user_id' in 'where clause' in C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT COUNT(*)...', 0)
#1 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT COUNT(*)...')
#2 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT COUNT(*)...')
#3 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1733): CodeIgniter\Database\BaseConnection->query('SELECT COUNT(*)...', Array, false)
#4 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Model.php(676): CodeIgniter\Database\BaseBuilder->countAllResults(true)
#5 C:\sertifikat\myapp\app\Controllers\Events.php(273): CodeIgniter\Model->countAllResults()
#6 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Events->certificate(3)
#7 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
#8 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#10 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 C:\sertifikat\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 C:\sertifikat\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\m...')
#13 {main}
CRITICAL - 2025-05-31 12:28:03 --> CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'user_id' in 'where clause'
[Method: GET, Route: events/certificate/3]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1733): CodeIgniter\Database\BaseConnection->query('SELECT COUNT(*) AS `numrows`
FROM `participants`
WHERE `event_id` = :event_id:
AND `user_id` = :user_id:', [...], false)
 2 SYSTEMPATH\Model.php(676): CodeIgniter\Database\BaseBuilder->countAllResults(true)
 3 APPPATH\Controllers\Events.php(273): CodeIgniter\Model->countAllResults()
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->certificate(3)
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
10 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 12:28:03 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Unknown column 'user_id' in 'where clause'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT COUNT(*) AS `numrows`
FROM `participants`
WHERE `event_id` = 3
AND `user_id` = \'6\'')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT COUNT(*) AS `numrows`
FROM `participants`
WHERE `event_id` = 3
AND `user_id` = \'6\'')
 3 SYSTEMPATH\Database\BaseBuilder.php(1733): CodeIgniter\Database\BaseConnection->query('SELECT COUNT(*) AS `numrows`
FROM `participants`
WHERE `event_id` = :event_id:
AND `user_id` = :user_id:', [...], false)
 4 SYSTEMPATH\Model.php(676): CodeIgniter\Database\BaseBuilder->countAllResults(true)
 5 APPPATH\Controllers\Events.php(273): CodeIgniter\Model->countAllResults()
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->certificate(3)
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 12:28:03 --> [Caused by] mysqli_sql_exception: Unknown column 'user_id' in 'where clause'
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT COUNT(*) AS `numrows`
FROM `participants`
WHERE `event_id` = 3
AND `user_id` = \'6\'', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT COUNT(*) AS `numrows`
FROM `participants`
WHERE `event_id` = 3
AND `user_id` = \'6\'')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT COUNT(*) AS `numrows`
FROM `participants`
WHERE `event_id` = 3
AND `user_id` = \'6\'')
 4 SYSTEMPATH\Database\BaseBuilder.php(1733): CodeIgniter\Database\BaseConnection->query('SELECT COUNT(*) AS `numrows`
FROM `participants`
WHERE `event_id` = :event_id:
AND `user_id` = :user_id:', [...], false)
 5 SYSTEMPATH\Model.php(676): CodeIgniter\Database\BaseBuilder->countAllResults(true)
 6 APPPATH\Controllers\Events.php(273): CodeIgniter\Model->countAllResults()
 7 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->certificate(3)
 8 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 9 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
11 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
12 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
13 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 12:29:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:29:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:29:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:29:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:29:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:29:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:29:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:29:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:29:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:29:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:29:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:29:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:29:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:29:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:29:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:29:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:29:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:29:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:29:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:29:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:29:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:30:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:30:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:30:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:30:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:30:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:31:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:31:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:31:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:31:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:31:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:31:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:31:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:31:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:31:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:31:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:31:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:31:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:31:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:32:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:32:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:32:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:32:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:32:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:32:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:32:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:32:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:33:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:33:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:33:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:33:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:33:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:34:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:34:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:34:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:34:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:34:14 --> User ID: 6
DEBUG - 2025-05-31 12:34:14 --> Event ID: 3
DEBUG - 2025-05-31 12:34:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:34:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:34:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:34:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:35:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:35:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:35:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:35:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:35:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:35:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:35:29 --> User ID: 6
DEBUG - 2025-05-31 12:35:29 --> Event ID: 3
DEBUG - 2025-05-31 12:35:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:35:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:35:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:36:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:36:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:36:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:36:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:36:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:36:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:36:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:36:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:36:25 --> User ID: 6
DEBUG - 2025-05-31 12:36:25 --> Event ID: 4
DEBUG - 2025-05-31 12:36:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:37:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:37:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:37:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:37:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:37:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:37:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:37:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:37:46 --> User ID: 6
DEBUG - 2025-05-31 12:37:46 --> Event ID: 4
DEBUG - 2025-05-31 12:37:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:37:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:38:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:38:02 --> User ID: 6
DEBUG - 2025-05-31 12:38:02 --> Event ID: 2
DEBUG - 2025-05-31 12:38:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:38:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:38:08 --> User ID: 6
DEBUG - 2025-05-31 12:38:08 --> Event ID: 3
DEBUG - 2025-05-31 12:39:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:39:07 --> User ID: 6
DEBUG - 2025-05-31 12:39:07 --> Event ID: 3
DEBUG - 2025-05-31 12:40:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:40:01 --> User ID: 6
DEBUG - 2025-05-31 12:40:01 --> Event ID: 3
DEBUG - 2025-05-31 12:40:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:40:45 --> User ID: 6
DEBUG - 2025-05-31 12:40:45 --> Event ID: 3
DEBUG - 2025-05-31 12:41:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:41:30 --> User ID: 6
DEBUG - 2025-05-31 12:41:30 --> Event ID: 3
DEBUG - 2025-05-31 12:42:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:42:43 --> User ID: 6
DEBUG - 2025-05-31 12:42:43 --> Event ID: 3
DEBUG - 2025-05-31 12:42:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:42:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:42:49 --> User ID: 6
DEBUG - 2025-05-31 12:42:49 --> Event ID: 2
DEBUG - 2025-05-31 12:43:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:43:52 --> User ID: 6
DEBUG - 2025-05-31 12:43:52 --> Event ID: 2
DEBUG - 2025-05-31 12:43:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:43:56 --> User ID: 6
DEBUG - 2025-05-31 12:43:56 --> Event ID: 2
DEBUG - 2025-05-31 12:43:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:43:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:43:59 --> User ID: 6
DEBUG - 2025-05-31 12:43:59 --> Event ID: 2
DEBUG - 2025-05-31 12:44:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:44:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:44:40 --> User ID: 6
DEBUG - 2025-05-31 12:44:40 --> Event ID: 2
DEBUG - 2025-05-31 12:44:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:44:43 --> User ID: 6
DEBUG - 2025-05-31 12:44:43 --> Event ID: 2
DEBUG - 2025-05-31 12:44:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:44:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:44:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 12:44:51 --> User ID: 6
DEBUG - 2025-05-31 12:44:51 --> Event ID: 2
DEBUG - 2025-05-31 13:44:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:44:49 --> User ID: 6
DEBUG - 2025-05-31 13:44:49 --> Event ID: 
DEBUG - 2025-05-31 13:44:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:44:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:44:55 --> User ID: 6
DEBUG - 2025-05-31 13:44:55 --> Event ID: 
DEBUG - 2025-05-31 13:44:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:44:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:45:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:45:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:45:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:45:01 --> User ID: 6
DEBUG - 2025-05-31 13:45:01 --> Event ID: 
DEBUG - 2025-05-31 13:45:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:45:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:45:05 --> User ID: 6
DEBUG - 2025-05-31 13:45:05 --> Event ID: 
DEBUG - 2025-05-31 13:45:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:45:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:45:06 --> User ID: 6
DEBUG - 2025-05-31 13:45:06 --> Event ID: 
DEBUG - 2025-05-31 13:45:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:45:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:45:08 --> User ID: 6
DEBUG - 2025-05-31 13:45:08 --> Event ID: 
DEBUG - 2025-05-31 13:45:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:45:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:45:08 --> User ID: 6
DEBUG - 2025-05-31 13:45:08 --> Event ID: 
DEBUG - 2025-05-31 13:45:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:45:08 --> User ID: 6
DEBUG - 2025-05-31 13:45:08 --> Event ID: 
DEBUG - 2025-05-31 13:45:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:45:08 --> User ID: 6
DEBUG - 2025-05-31 13:45:08 --> Event ID: 
DEBUG - 2025-05-31 13:45:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:47:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:47:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:47:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:47:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:47:51 --> User ID: 6
DEBUG - 2025-05-31 13:47:51 --> Event ID: 3
DEBUG - 2025-05-31 13:47:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:47:56 --> User ID: 6
DEBUG - 2025-05-31 13:47:56 --> Event ID: 
DEBUG - 2025-05-31 13:47:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:47:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:47:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:53:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:53:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:53:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:53:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:53:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:53:38 --> User ID: 6
DEBUG - 2025-05-31 13:53:38 --> Event ID: 3
DEBUG - 2025-05-31 13:53:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:53:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:53:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:53:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:53:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:53:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:53:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:53:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:53:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 13:53:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:01:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:01:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:01:25 --> User ID: 6
DEBUG - 2025-05-31 14:01:25 --> Event ID: 1
DEBUG - 2025-05-31 14:01:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:01:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:01:31 --> User ID: 6
DEBUG - 2025-05-31 14:01:31 --> Event ID: 4
DEBUG - 2025-05-31 14:01:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:01:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:01:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:01:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:01:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:02:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:02:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:02:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:03:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:03:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:05:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:06:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:06:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:06:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:06:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:06:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:06:36 --> User ID: 6
DEBUG - 2025-05-31 14:06:36 --> Event ID: 4
DEBUG - 2025-05-31 14:06:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:06:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:06:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:06:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:06:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:06:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:06:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:06:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:06:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:06:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:07:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:07:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:07:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:07:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:12:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:12:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:12:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:12:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:12:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:12:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:12:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:12:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:12:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:12:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:12:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:12:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:12:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:12:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:12:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:12:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:12:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:12:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:12:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:12:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:12:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:12:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:16:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:16:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:16:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:16:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:16:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:16:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:16:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:16:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:16:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:16:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:16:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:16:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:16:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:17:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:17:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:17:02 --> User ID: 1
DEBUG - 2025-05-31 14:17:02 --> Event ID: 2
DEBUG - 2025-05-31 14:17:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:17:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:17:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:17:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:17:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:17:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:17:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:17:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:17:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:19:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:20:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:20:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:20:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:20:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:20:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:20:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:21:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:26:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:26:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:26:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:26:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:26:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:26:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:26:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:29:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:29:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:29:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:29:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:29:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:30:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:30:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:30:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:30:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:30:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:30:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:30:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:30:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:31:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:31:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:31:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:31:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:31:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:32:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:32:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:32:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:32:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:32:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:32:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:32:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:32:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:32:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:32:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:32:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:35:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:35:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:35:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:35:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:35:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:35:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:35:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:35:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:35:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:35:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:35:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:35:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:35:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:36:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:36:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:36:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:36:02 --> User ID: 6
DEBUG - 2025-05-31 14:36:02 --> Event ID: 5
DEBUG - 2025-05-31 14:36:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:36:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:36:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:36:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:36:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:36:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:36:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:36:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:36:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:36:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:36:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:38:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:38:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:39:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:39:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:39:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:39:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:39:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:39:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:41:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:41:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:41:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:41:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:41:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:41:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:41:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:41:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:42:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:42:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:42:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:42:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:42:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:42:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:42:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:42:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:42:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:42:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:43:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:43:12 --> SQL Query: 
DEBUG - 2025-05-31 14:44:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:44:19 --> SQL Query: 
DEBUG - 2025-05-31 14:44:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:44:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:44:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:44:37 --> SQL Query: 
DEBUG - 2025-05-31 14:44:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:44:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:44:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:44:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:45:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:45:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:45:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:45:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:45:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:45:25 --> SQL Query: 
DEBUG - 2025-05-31 14:45:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:45:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:45:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:46:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:46:00 --> SQL Query: 
DEBUG - 2025-05-31 14:46:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 14:46:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:09:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:09:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:09:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:13:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:23:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:24:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:24:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:24:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:24:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:24:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:24:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:24:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:24:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:24:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:24:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:25:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:25:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:25:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:25:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:25:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:25:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:26:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:26:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:26:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:26:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:26:20 --> SQL Query: 
DEBUG - 2025-05-31 15:26:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:26:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:27:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:27:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:29:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:29:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:29:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:29:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:29:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:30:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:30:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:30:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:30:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:30:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:30:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:31:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:31:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:31:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:32:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:32:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:33:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:34:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:34:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:35:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:35:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:35:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:35:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:35:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:35:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:35:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:37:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:37:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:37:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:38:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:38:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 15:38:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-31 15:42:26 --> ParseError: syntax error, unexpected token "public"
[Method: GET, Route: login]
in APPPATH\Controllers\Auth.php on line 63.
 1 VENDORPATH\composer\ClassLoader.php(427): Composer\Autoload\{closure}('C:\\sertifikat\\myapp\\vendor\\composer/../../app\\Controllers\\Auth.php')
 2 [internal function]: Composer\Autoload\ClassLoader->loadClass('App\\Controllers\\Auth')
 3 SYSTEMPATH\CodeIgniter.php(885): class_exists('\\App\\Controllers\\Auth', true)
 4 SYSTEMPATH\CodeIgniter.php(494): CodeIgniter\CodeIgniter->startController()
 5 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 8 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 9 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 15:44:26 --> ParseError: syntax error, unexpected token "public"
[Method: GET, Route: auth/adminLogin]
in APPPATH\Controllers\Auth.php on line 63.
 1 VENDORPATH\composer\ClassLoader.php(427): Composer\Autoload\{closure}('C:\\sertifikat\\myapp\\vendor\\composer/../../app\\Controllers\\Auth.php')
 2 [internal function]: Composer\Autoload\ClassLoader->loadClass('App\\Controllers\\Auth')
 3 SYSTEMPATH\CodeIgniter.php(885): class_exists('\\App\\Controllers\\Auth', true)
 4 SYSTEMPATH\CodeIgniter.php(494): CodeIgniter\CodeIgniter->startController()
 5 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 8 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 9 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 15:46:24 --> ParseError: syntax error, unexpected token "public"
[Method: GET, Route: auth/adminLogin]
in APPPATH\Controllers\Auth.php on line 63.
 1 VENDORPATH\composer\ClassLoader.php(427): Composer\Autoload\{closure}('C:\\sertifikat\\myapp\\vendor\\composer/../../app\\Controllers\\Auth.php')
 2 [internal function]: Composer\Autoload\ClassLoader->loadClass('App\\Controllers\\Auth')
 3 SYSTEMPATH\CodeIgniter.php(885): class_exists('\\App\\Controllers\\Auth', true)
 4 SYSTEMPATH\CodeIgniter.php(494): CodeIgniter\CodeIgniter->startController()
 5 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 8 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 9 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 15:46:25 --> ParseError: syntax error, unexpected token "public"
[Method: GET, Route: auth/adminLogin]
in APPPATH\Controllers\Auth.php on line 63.
 1 VENDORPATH\composer\ClassLoader.php(427): Composer\Autoload\{closure}('C:\\sertifikat\\myapp\\vendor\\composer/../../app\\Controllers\\Auth.php')
 2 [internal function]: Composer\Autoload\ClassLoader->loadClass('App\\Controllers\\Auth')
 3 SYSTEMPATH\CodeIgniter.php(885): class_exists('\\App\\Controllers\\Auth', true)
 4 SYSTEMPATH\CodeIgniter.php(494): CodeIgniter\CodeIgniter->startController()
 5 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 8 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 9 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 15:47:02 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "pages/auth/adminLogin.php"
[Method: GET, Route: auth/adminLogin]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('pages/auth/adminLogin.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/auth/adminLogin', [], true)
 3 APPPATH\Controllers\Auth.php(62): view('pages/auth/adminLogin')
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->adminLogin()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
10 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 15:48:19 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "pages/auth/adminLogin.php"
[Method: GET, Route: auth/adminLogin]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('pages/auth/adminLogin.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/auth/adminLogin', [], true)
 3 APPPATH\Controllers\Auth.php(62): view('pages/auth/adminLogin')
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->adminLogin()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
10 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 15:48:40 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "pages/auth/adminLogin.php"
[Method: GET, Route: auth/adminLogin]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('pages/auth/adminLogin.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/auth/adminLogin', [], true)
 3 APPPATH\Controllers\Auth.php(62): view('pages/auth/adminLogin')
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->adminLogin()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
10 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 15:48:48 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "pages/auth/adminLogin.php"
[Method: GET, Route: auth/adminLogin]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('pages/auth/adminLogin.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/auth/adminLogin', [], true)
 3 APPPATH\Controllers\Auth.php(62): view('pages/auth/adminLogin')
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->adminLogin()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
10 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 15:52:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-31 15:52:36 --> Error: Call to undefined function set_value()
[Method: GET, Route: auth/adminLogin]
in APPPATH\Views\pages\auth\admin_login.php on line 11.
 1 SYSTEMPATH\View\View.php(224): include()
 2 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 3 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/auth/admin_login', [], true)
 4 APPPATH\Controllers\Auth.php(62): view('pages/auth/admin_login')
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->adminLogin()
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 15:56:17 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "pages/auth/adminLogin.php"
[Method: GET, Route: auth/adminLogin]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('pages/auth/adminLogin.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/auth/adminLogin', [], true)
 3 APPPATH\Controllers\Auth.php(63): view('pages/auth/adminLogin')
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->adminLogin()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
10 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 15:56:19 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "pages/auth/adminLogin.php"
[Method: GET, Route: auth/adminLogin]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('pages/auth/adminLogin.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/auth/adminLogin', [], true)
 3 APPPATH\Controllers\Auth.php(63): view('pages/auth/adminLogin')
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->adminLogin()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
10 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 15:56:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-31 15:59:44 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "pages/auth/adminLogin.php"
[Method: GET, Route: auth/adminLogin]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('pages/auth/adminLogin.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/auth/adminLogin', [], true)
 3 APPPATH\Controllers\Auth.php(63): view('pages/auth/adminLogin')
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->adminLogin()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
10 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 16:06:47 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "pages/auth/adminLogin.php"
[Method: GET, Route: auth/adminLogin]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('pages/auth/adminLogin.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/auth/adminLogin', [], true)
 3 APPPATH\Controllers\Auth.php(63): view('pages/auth/adminLogin')
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->adminLogin()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
10 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
CRITICAL - 2025-05-31 16:06:49 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "pages/auth/adminLogin.php"
[Method: GET, Route: auth/adminLogin]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('pages/auth/adminLogin.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/auth/adminLogin', [], true)
 3 APPPATH\Controllers\Auth.php(63): view('pages/auth/adminLogin')
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->adminLogin()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
10 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 16:07:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:07:54 --> SQL Query: 
DEBUG - 2025-05-31 16:07:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:07:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-31 16:08:05 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "pages/auth/adminLogin.php"
[Method: GET, Route: auth/adminLogin]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('pages/auth/adminLogin.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/auth/adminLogin', [], true)
 3 APPPATH\Controllers\Auth.php(63): view('pages/auth/adminLogin')
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->adminLogin()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
10 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 16:24:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:25:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-05-31 16:27:17 --> ErrorException: Trying to access array offset on value of type null
[Method: GET, Route: participants/certificate/5]
in APPPATH\Controllers\Participants.php on line 74.
 1 APPPATH\Controllers\Participants.php(74): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Trying to access array offset on value of type null', 'C:\\sertifikat\\myapp\\app\\Controllers\\Participants.php', 74)
 2 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Participants->certificate('5')
 3 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Participants))
 4 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 7 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 8 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-05-31 16:27:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:27:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:27:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:27:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:27:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:27:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:27:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:27:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:27:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:28:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:29:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:29:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:29:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:30:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:30:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:30:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:30:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:30:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:30:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:30:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:30:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:30:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:30:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:30:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:30:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:30:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:30:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:30:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:31:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:31:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:31:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:31:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:31:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:31:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:31:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:31:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:31:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:31:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:32:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:32:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:32:08 --> SQL Query: 
DEBUG - 2025-05-31 16:32:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:32:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:32:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:32:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:32:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:32:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:32:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:32:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:32:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:32:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:32:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:32:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:32:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:32:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:36:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:37:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:37:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:37:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:37:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:37:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:37:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:37:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:37:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:37:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:37:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:37:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:37:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:37:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:37:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:37:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:37:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:37:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:37:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:37:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:42:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:42:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:42:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:42:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:42:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:42:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:42:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:42:32 --> SQL Query: 
DEBUG - 2025-05-31 16:42:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:42:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:42:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:42:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-05-31 16:42:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
