DEBUG - 2025-06-03 10:31:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:31:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:39:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-03 10:39:06 --> ErrorException: Undefined variable $page
[Method: GET, Route: login]
in APPPATH\Views\layout\main.php on line 61.
 1 APPPATH\Views\layout\main.php(61): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined variable $page', 'C:\\sertifikat\\myapp\\app\\Views\\layout\\main.php', 61)
 2 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\myapp\\app\\Views\\layout\\main.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\View\View.php(240): CodeIgniter\View\View->render('layout/main', [], true)
 5 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/auth/login', [], true)
 6 APPPATH\Controllers\Auth.php(11): view('pages/auth/login')
 7 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->login()
 8 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
 9 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
11 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
12 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
13 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-06-03 10:39:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-03 10:39:13 --> ErrorException: Undefined variable $page
[Method: GET, Route: login]
in APPPATH\Views\layout\main.php on line 61.
 1 APPPATH\Views\layout\main.php(61): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined variable $page', 'C:\\sertifikat\\myapp\\app\\Views\\layout\\main.php', 61)
 2 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\myapp\\app\\Views\\layout\\main.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\View\View.php(240): CodeIgniter\View\View->render('layout/main', [], true)
 5 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/auth/login', [], true)
 6 APPPATH\Controllers\Auth.php(11): view('pages/auth/login')
 7 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->login()
 8 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
 9 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
11 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
12 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
13 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-06-03 10:39:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-03 10:40:00 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "pages/login.php"
[Method: GET, Route: login]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('pages/login.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/login', [], true)
 3 APPPATH\Views\layout\main.php(137): view('pages/login')
 4 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\myapp\\app\\Views\\layout\\main.php')
 5 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 6 SYSTEMPATH\View\View.php(240): CodeIgniter\View\View->render('layout/main', [], true)
 7 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/auth/login', [], true)
 8 APPPATH\Controllers\Auth.php(12): view('pages/auth/login', [...])
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->login()
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
15 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-06-03 10:40:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-03 10:40:00 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "pages/login.php"
[Method: GET, Route: login]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('pages/login.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/login', [], true)
 3 APPPATH\Views\layout\main.php(137): view('pages/login')
 4 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\myapp\\app\\Views\\layout\\main.php')
 5 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 6 SYSTEMPATH\View\View.php(240): CodeIgniter\View\View->render('layout/main', [], true)
 7 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/auth/login', [], true)
 8 APPPATH\Controllers\Auth.php(12): view('pages/auth/login', [...])
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->login()
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
15 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-06-03 10:40:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-03 10:40:02 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "pages/login.php"
[Method: GET, Route: login]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('pages/login.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/login', [], true)
 3 APPPATH\Views\layout\main.php(137): view('pages/login')
 4 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\myapp\\app\\Views\\layout\\main.php')
 5 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 6 SYSTEMPATH\View\View.php(240): CodeIgniter\View\View->render('layout/main', [], true)
 7 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/auth/login', [], true)
 8 APPPATH\Controllers\Auth.php(12): view('pages/auth/login', [...])
 9 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->login()
10 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
11 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
12 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
14 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
15 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\myapp\\public\\index.php')
DEBUG - 2025-06-03 10:40:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-03 10:40:53 --> ErrorException: Allowed memory size of 536870912 bytes exhausted (tried to allocate 32768 bytes)
[Method: GET, Route: login]
in APPPATH\Views\pages\auth\login.php on line 1.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
DEBUG - 2025-06-03 10:40:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-03 10:41:06 --> ErrorException: Allowed memory size of 536870912 bytes exhausted (tried to allocate 32768 bytes)
[Method: GET, Route: login]
in APPPATH\Views\pages\auth\login.php on line 1.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
DEBUG - 2025-06-03 10:41:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-03 10:41:56 --> ErrorException: Allowed memory size of 536870912 bytes exhausted (tried to allocate 32768 bytes)
[Method: GET, Route: login]
in APPPATH\Views\pages\auth\login.php on line 1.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
DEBUG - 2025-06-03 10:41:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-03 10:42:08 --> ErrorException: Allowed memory size of 536870912 bytes exhausted (tried to allocate 32768 bytes)
[Method: GET, Route: login]
in APPPATH\Views\pages\auth\login.php on line 1.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
DEBUG - 2025-06-03 10:43:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-03 10:44:04 --> ErrorException: Allowed memory size of 536870912 bytes exhausted (tried to allocate 32768 bytes)
[Method: GET, Route: login]
in APPPATH\Views\pages\auth\login.php on line 1.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
DEBUG - 2025-06-03 10:44:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-03 10:44:15 --> ErrorException: Allowed memory size of 536870912 bytes exhausted (tried to allocate 32768 bytes)
[Method: GET, Route: login]
in APPPATH\Views\pages\auth\login.php on line 1.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
DEBUG - 2025-06-03 10:44:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-03 10:44:25 --> ErrorException: Allowed memory size of 536870912 bytes exhausted (tried to allocate 32768 bytes)
[Method: GET, Route: login]
in APPPATH\Views\pages\auth\login.php on line 1.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
DEBUG - 2025-06-03 10:44:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-03 10:44:53 --> ErrorException: Allowed memory size of 536870912 bytes exhausted (tried to allocate 32768 bytes)
[Method: GET, Route: login]
in APPPATH\Views\pages\auth\login.php on line 1.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
DEBUG - 2025-06-03 10:44:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-03 10:45:03 --> ErrorException: Allowed memory size of 536870912 bytes exhausted (tried to allocate 32768 bytes)
[Method: GET, Route: login]
in APPPATH\Views\pages\auth\login.php on line 1.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
DEBUG - 2025-06-03 10:45:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-03 10:45:13 --> ErrorException: Allowed memory size of 536870912 bytes exhausted (tried to allocate 32768 bytes)
[Method: GET, Route: login]
in APPPATH\Views\pages\auth\login.php on line 1.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
DEBUG - 2025-06-03 10:45:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-03 10:45:24 --> ErrorException: Allowed memory size of 536870912 bytes exhausted (tried to allocate 32768 bytes)
[Method: GET, Route: login]
in APPPATH\Views\pages\auth\login.php on line 1.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
DEBUG - 2025-06-03 10:45:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:46:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:46:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:46:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:46:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:46:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:46:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:46:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:46:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:47:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:47:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:47:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:47:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:47:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:47:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:47:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:47:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:47:22 --> Current Time for Certificate Query: 2025-06-03 10:47:22
DEBUG - 2025-06-03 10:47:22 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '8'
DEBUG - 2025-06-03 10:47:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:47:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:47:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:47:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:47:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:48:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:48:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:48:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:48:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:48:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:48:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:48:15 --> User ID: 8
DEBUG - 2025-06-03 10:48:15 --> Event ID: 10
DEBUG - 2025-06-03 10:48:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:48:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:48:23 --> Current Time for Certificate Query: 2025-06-03 10:48:23
DEBUG - 2025-06-03 10:48:23 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '8'
DEBUG - 2025-06-03 10:48:23 --> Event: Seminar bareng freya jayawardana, EndDate: 2025-06-03 10:49:00, IsCompleted: false
DEBUG - 2025-06-03 10:50:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:50:27 --> Current Time for Certificate Query: 2025-06-03 10:50:27
DEBUG - 2025-06-03 10:50:27 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '8'
DEBUG - 2025-06-03 10:50:27 --> Event: Seminar bareng freya jayawardana, EndDate: 2025-06-03 10:49:00, IsCompleted: true
DEBUG - 2025-06-03 10:50:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:50:29 --> User ID: 8
DEBUG - 2025-06-03 10:50:29 --> Event ID: 10
DEBUG - 2025-06-03 10:50:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:50:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:50:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:50:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:51:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:51:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:51:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:51:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:51:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-03 10:51:15 --> Berhasil Daftar: <EMAIL> untuk event 11
DEBUG - 2025-06-03 10:51:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:51:15 --> User ID: 8
DEBUG - 2025-06-03 10:51:15 --> Event ID: 11
DEBUG - 2025-06-03 10:51:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:51:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:51:19 --> Current Time for Certificate Query: 2025-06-03 10:51:19
DEBUG - 2025-06-03 10:51:19 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '8'
DEBUG - 2025-06-03 10:51:19 --> Event: Seminar bareng freya jayawardana, EndDate: 2025-06-03 10:49:00, IsCompleted: true
DEBUG - 2025-06-03 10:51:19 --> Event: Seminar Bareng Oline, EndDate: 2025-06-03 10:53:00, IsCompleted: false
DEBUG - 2025-06-03 10:51:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:51:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:51:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:51:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:51:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:51:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:51:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-03 10:51:59 --> Berhasil Daftar: <EMAIL> untuk event 12
DEBUG - 2025-06-03 10:51:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:51:59 --> User ID: 8
DEBUG - 2025-06-03 10:51:59 --> Event ID: 12
DEBUG - 2025-06-03 10:51:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:53:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:53:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:53:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:53:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:53:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:53:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-03 10:53:53 --> Berhasil Daftar: <EMAIL> untuk event 13
DEBUG - 2025-06-03 10:53:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:53:53 --> User ID: 8
DEBUG - 2025-06-03 10:53:53 --> Event ID: 13
DEBUG - 2025-06-03 10:55:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:55:22 --> User ID: 8
DEBUG - 2025-06-03 10:55:22 --> Event ID: 13
DEBUG - 2025-06-03 10:55:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:55:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:55:27 --> Current Time for Certificate Query: 2025-06-03 10:55:27
DEBUG - 2025-06-03 10:55:27 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '8'
DEBUG - 2025-06-03 10:55:27 --> Event: Seminar bareng freya jayawardana, EndDate: 2025-06-03 10:49:00, IsCompleted: true
DEBUG - 2025-06-03 10:55:27 --> Event: Seminar Bareng Oline, EndDate: 2025-06-03 10:53:00, IsCompleted: true
DEBUG - 2025-06-03 10:55:27 --> Event: Sertifikat hanya tersedia setelah event selesai., EndDate: 2025-06-03 10:53:00, IsCompleted: true
DEBUG - 2025-06-03 10:55:27 --> Event: Seminar Bareng Shani Indira, EndDate: 2025-06-03 10:55:00, IsCompleted: true
DEBUG - 2025-06-03 10:55:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:55:30 --> User ID: 8
DEBUG - 2025-06-03 10:55:30 --> Event ID: 13
DEBUG - 2025-06-03 10:55:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:55:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:55:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:55:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:55:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:55:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:55:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:56:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:56:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:56:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:56:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:56:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-03 10:56:17 --> Berhasil Daftar: <EMAIL> untuk event 14
DEBUG - 2025-06-03 10:56:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:56:17 --> User ID: 8
DEBUG - 2025-06-03 10:56:17 --> Event ID: 14
DEBUG - 2025-06-03 10:56:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:56:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:56:26 --> Current Time for Certificate Query: 2025-06-03 10:56:26
DEBUG - 2025-06-03 10:56:26 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '8'
DEBUG - 2025-06-03 10:56:26 --> Event: Seminar bareng freya jayawardana, EndDate: 2025-06-03 10:49:00, IsCompleted: true
DEBUG - 2025-06-03 10:56:26 --> Event: Seminar Bareng Oline, EndDate: 2025-06-03 10:53:00, IsCompleted: true
DEBUG - 2025-06-03 10:56:26 --> Event: Sertifikat hanya tersedia setelah event selesai., EndDate: 2025-06-03 10:53:00, IsCompleted: true
DEBUG - 2025-06-03 10:56:26 --> Event: Seminar Bareng Shani Indira, EndDate: 2025-06-03 10:55:00, IsCompleted: true
DEBUG - 2025-06-03 10:56:26 --> Event: Seminar Bareng Alvin alfandy, EndDate: 2025-06-03 10:58:00, IsCompleted: false
DEBUG - 2025-06-03 10:57:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:57:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:58:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:58:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:58:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:58:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:58:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:58:16 --> Current Time for Certificate Query: 2025-06-03 10:58:16
DEBUG - 2025-06-03 10:58:16 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '8'
DEBUG - 2025-06-03 10:58:16 --> Event: Seminar bareng freya jayawardana, EndDate: 2025-06-03 10:49:00, IsCompleted: true
DEBUG - 2025-06-03 10:58:16 --> Event: Seminar Bareng Oline, EndDate: 2025-06-03 10:53:00, IsCompleted: true
DEBUG - 2025-06-03 10:58:16 --> Event: Sertifikat hanya tersedia setelah event selesai., EndDate: 2025-06-03 10:53:00, IsCompleted: true
DEBUG - 2025-06-03 10:58:16 --> Event: Seminar Bareng Shani Indira, EndDate: 2025-06-03 10:55:00, IsCompleted: true
DEBUG - 2025-06-03 10:58:16 --> Event: Seminar Bareng Alvin alfandy, EndDate: 2025-06-03 10:58:00, IsCompleted: true
DEBUG - 2025-06-03 10:58:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:58:18 --> User ID: 8
DEBUG - 2025-06-03 10:58:18 --> Event ID: 14
DEBUG - 2025-06-03 10:58:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:58:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:58:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:58:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:58:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-03 10:58:25 --> Berhasil Daftar: <EMAIL> untuk event 15
DEBUG - 2025-06-03 10:58:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:58:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:58:37 --> Current Time for Certificate Query: 2025-06-03 10:58:37
DEBUG - 2025-06-03 10:58:37 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '8'
DEBUG - 2025-06-03 10:58:37 --> Event: Seminar bareng freya jayawardana, EndDate: 2025-06-03 10:49:00, IsCompleted: true
DEBUG - 2025-06-03 10:58:37 --> Event: Seminar Bareng Oline, EndDate: 2025-06-03 10:53:00, IsCompleted: true
DEBUG - 2025-06-03 10:58:37 --> Event: Sertifikat hanya tersedia setelah event selesai., EndDate: 2025-06-03 10:53:00, IsCompleted: true
DEBUG - 2025-06-03 10:58:37 --> Event: Seminar Bareng Shani Indira, EndDate: 2025-06-03 10:55:00, IsCompleted: true
DEBUG - 2025-06-03 10:58:37 --> Event: Seminar Bareng Alvin alfandy, EndDate: 2025-06-03 10:58:00, IsCompleted: true
DEBUG - 2025-06-03 10:58:37 --> Event: Seminar Bareng Marsha Lenathea Lapian, EndDate: 2025-06-03 10:59:00, IsCompleted: false
DEBUG - 2025-06-03 10:58:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:59:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:59:08 --> Current Time for Certificate Query: 2025-06-03 10:59:08
DEBUG - 2025-06-03 10:59:08 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '8'
DEBUG - 2025-06-03 10:59:08 --> Event: Seminar bareng freya jayawardana, EndDate: 2025-06-03 10:49:00, IsCompleted: true
DEBUG - 2025-06-03 10:59:08 --> Event: Seminar Bareng Oline, EndDate: 2025-06-03 10:53:00, IsCompleted: true
DEBUG - 2025-06-03 10:59:08 --> Event: Sertifikat hanya tersedia setelah event selesai., EndDate: 2025-06-03 10:53:00, IsCompleted: true
DEBUG - 2025-06-03 10:59:08 --> Event: Seminar Bareng Shani Indira, EndDate: 2025-06-03 10:55:00, IsCompleted: true
DEBUG - 2025-06-03 10:59:08 --> Event: Seminar Bareng Alvin alfandy, EndDate: 2025-06-03 10:58:00, IsCompleted: true
DEBUG - 2025-06-03 10:59:08 --> Event: Seminar Bareng Marsha Lenathea Lapian, EndDate: 2025-06-03 10:59:00, IsCompleted: true
DEBUG - 2025-06-03 10:59:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 10:59:13 --> User ID: 8
DEBUG - 2025-06-03 10:59:13 --> Event ID: 15
DEBUG - 2025-06-03 14:21:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 14:21:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 14:21:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 14:21:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 14:21:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 14:21:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 14:21:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 14:22:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 14:22:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 14:22:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 14:22:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 14:22:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 14:22:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 14:22:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 14:23:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 14:23:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 14:23:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 14:23:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 14:23:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-03 14:23:54 --> Berhasil Daftar: <EMAIL> untuk event 16
DEBUG - 2025-06-03 14:23:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 14:23:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 14:23:58 --> Current Time for Certificate Query: 2025-06-03 14:23:58
DEBUG - 2025-06-03 14:23:58 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '8'
DEBUG - 2025-06-03 14:23:58 --> Event: Seminar bareng freya jayawardana, EndDate: 2025-06-03 10:49:00, IsCompleted: true
DEBUG - 2025-06-03 14:23:58 --> Event: Seminar Bareng Oline, EndDate: 2025-06-03 10:53:00, IsCompleted: true
DEBUG - 2025-06-03 14:23:58 --> Event: Sertifikat hanya tersedia setelah event selesai., EndDate: 2025-06-03 10:53:00, IsCompleted: true
DEBUG - 2025-06-03 14:23:58 --> Event: Seminar Bareng Shani Indira, EndDate: 2025-06-03 10:55:00, IsCompleted: true
DEBUG - 2025-06-03 14:23:58 --> Event: Seminar Bareng Alvin alfandy, EndDate: 2025-06-03 10:58:00, IsCompleted: true
DEBUG - 2025-06-03 14:23:58 --> Event: Seminar Bareng Marsha Lenathea Lapian, EndDate: 2025-06-03 10:59:00, IsCompleted: true
DEBUG - 2025-06-03 14:23:58 --> Event: sdf, EndDate: 2025-06-03 14:25:00, IsCompleted: false
DEBUG - 2025-06-03 14:24:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 14:24:08 --> Current Time for Certificate Query: 2025-06-03 14:24:08
DEBUG - 2025-06-03 14:24:08 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '8'
DEBUG - 2025-06-03 14:24:08 --> Event: Seminar bareng freya jayawardana, EndDate: 2025-06-03 10:49:00, IsCompleted: true
DEBUG - 2025-06-03 14:24:08 --> Event: Seminar Bareng Oline, EndDate: 2025-06-03 10:53:00, IsCompleted: true
DEBUG - 2025-06-03 14:24:08 --> Event: Sertifikat hanya tersedia setelah event selesai., EndDate: 2025-06-03 10:53:00, IsCompleted: true
DEBUG - 2025-06-03 14:24:08 --> Event: Seminar Bareng Shani Indira, EndDate: 2025-06-03 10:55:00, IsCompleted: true
DEBUG - 2025-06-03 14:24:08 --> Event: Seminar Bareng Alvin alfandy, EndDate: 2025-06-03 10:58:00, IsCompleted: true
DEBUG - 2025-06-03 14:24:08 --> Event: Seminar Bareng Marsha Lenathea Lapian, EndDate: 2025-06-03 10:59:00, IsCompleted: true
DEBUG - 2025-06-03 14:24:08 --> Event: sdf, EndDate: 2025-06-03 14:25:00, IsCompleted: false
DEBUG - 2025-06-03 14:24:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 14:24:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-03 14:24:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
