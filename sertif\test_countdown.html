<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Countdown Timer</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Countdown Timer Styles */
        .countdown-timer {
            margin-top: 10px;
        }

        .countdown-display {
            display: flex;
            justify-content: center;
            gap: 8px;
            flex-wrap: wrap;
        }

        .countdown-item {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            padding: 8px 6px;
            min-width: 45px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }

        .countdown-item:hover {
            transform: translateY(-2px);
        }

        .countdown-number {
            display: block;
            font-size: 16px;
            font-weight: bold;
            color: white;
            line-height: 1;
            transition: transform 0.1s ease;
        }

        .countdown-label {
            display: block;
            font-size: 10px;
            color: rgba(255,255,255,0.9);
            margin-top: 2px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .countdown-expired {
            color: #28a745;
            font-weight: bold;
            padding: 8px 12px;
            background: rgba(40, 167, 69, 0.1);
            border-radius: 6px;
            border: 1px solid rgba(40, 167, 69, 0.3);
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-clock"></i> Test Countdown Timer</h5>
                    </div>
                    <div class="card-body text-center">
                        <h6>Event akan berakhir dalam:</h6>
                        
                        <!-- Test countdown - set to 2 minutes from now -->
                        <div class="countdown-timer" id="test-countdown">
                            <div class="countdown-display">
                                <div class="countdown-item">
                                    <span class="countdown-number days">0</span>
                                    <span class="countdown-label">Hari</span>
                                </div>
                                <div class="countdown-item">
                                    <span class="countdown-number hours">0</span>
                                    <span class="countdown-label">Jam</span>
                                </div>
                                <div class="countdown-item">
                                    <span class="countdown-number minutes">0</span>
                                    <span class="countdown-label">Menit</span>
                                </div>
                                <div class="countdown-item">
                                    <span class="countdown-number seconds">0</span>
                                    <span class="countdown-label">Detik</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <small class="text-muted">Timer ini akan berakhir dalam 2 menit untuk testing</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Set end time to 2 minutes from now for testing
            const now = new Date();
            const endTime = new Date(now.getTime() + (2 * 60 * 1000)); // 2 minutes from now
            
            const timerElement = document.getElementById('test-countdown');
            startCountdown(timerElement, endTime.toISOString());
            
            function startCountdown(timerElement, endDateStr) {
                const endDate = new Date(endDateStr).getTime();
                
                const countdownInterval = setInterval(function() {
                    const now = new Date().getTime();
                    const distance = endDate - now;
                    
                    if (distance < 0) {
                        // Event has ended
                        clearInterval(countdownInterval);
                        timerElement.innerHTML = '<div class="countdown-expired"><i class="fas fa-check-circle"></i> Event Selesai! Sertifikat Tersedia</div>';
                        return;
                    }
                    
                    // Calculate time units
                    const days = Math.floor(distance / (1000 * 60 * 60 * 24));
                    const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                    const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                    const seconds = Math.floor((distance % (1000 * 60)) / 1000);
                    
                    // Update the display
                    const daysElement = timerElement.querySelector('.days');
                    const hoursElement = timerElement.querySelector('.hours');
                    const minutesElement = timerElement.querySelector('.minutes');
                    const secondsElement = timerElement.querySelector('.seconds');
                    
                    if (daysElement) daysElement.textContent = days.toString().padStart(2, '0');
                    if (hoursElement) hoursElement.textContent = hours.toString().padStart(2, '0');
                    if (minutesElement) minutesElement.textContent = minutes.toString().padStart(2, '0');
                    if (secondsElement) secondsElement.textContent = seconds.toString().padStart(2, '0');
                    
                    // Add animation effect when seconds change
                    if (secondsElement) {
                        secondsElement.style.transform = 'scale(1.1)';
                        setTimeout(function() {
                            secondsElement.style.transform = 'scale(1)';
                        }, 100);
                    }
                    
                }, 1000);
            }
        });
    </script>
</body>
</html>
