DEBUG - 2025-06-09 21:16:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:16:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:17:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:17:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:17:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:17:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:17:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:17:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:17:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:17:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:17:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:17:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:17:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:17:39 --> Current Time for Certificate Query: 2025-06-09 21:17:39
DEBUG - 2025-06-09 21:17:39 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-09 21:17:39 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-09 21:17:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:17:43 --> User ID: 9
DEBUG - 2025-06-09 21:17:43 --> Event ID: 17
DEBUG - 2025-06-09 21:17:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:17:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:23:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:23:56 --> User ID: 9
DEBUG - 2025-06-09 21:23:56 --> Event ID: 17
DEBUG - 2025-06-09 21:24:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:24:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:24:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:24:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:24:05 --> Current Time for Certificate Query: 2025-06-09 21:24:05
DEBUG - 2025-06-09 21:24:05 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-09 21:24:05 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-09 21:24:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:24:07 --> User ID: 9
DEBUG - 2025-06-09 21:24:07 --> Event ID: 17
DEBUG - 2025-06-09 21:24:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:24:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:24:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:24:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:24:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:25:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:25:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:25:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:25:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:25:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-09 21:25:10 --> Berhasil Daftar: <EMAIL> untuk event 18
DEBUG - 2025-06-09 21:25:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:25:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:25:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:25:14 --> Current Time for Certificate Query: 2025-06-09 21:25:14
DEBUG - 2025-06-09 21:25:14 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-09 21:25:14 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-09 21:25:14 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: false
DEBUG - 2025-06-09 21:26:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:26:40 --> Current Time for Certificate Query: 2025-06-09 21:26:40
DEBUG - 2025-06-09 21:26:40 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-09 21:26:40 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-09 21:26:40 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-09 21:26:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:26:42 --> User ID: 9
DEBUG - 2025-06-09 21:26:42 --> Event ID: 18
DEBUG - 2025-06-09 21:30:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:30:40 --> User ID: 9
DEBUG - 2025-06-09 21:30:40 --> Event ID: 18
DEBUG - 2025-06-09 21:30:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:31:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:31:00 --> User ID: 9
DEBUG - 2025-06-09 21:31:00 --> Event ID: 18
DEBUG - 2025-06-09 21:31:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:31:52 --> User ID: 9
DEBUG - 2025-06-09 21:31:52 --> Event ID: 18
DEBUG - 2025-06-09 21:31:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:31:55 --> User ID: 9
DEBUG - 2025-06-09 21:31:55 --> Event ID: 18
DEBUG - 2025-06-09 21:32:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:32:54 --> Current Time for Certificate Query: 2025-06-09 21:32:54
DEBUG - 2025-06-09 21:32:54 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-09 21:32:54 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-09 21:32:54 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-09 21:47:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:47:21 --> Current Time for Certificate Query: 2025-06-09 21:47:21
DEBUG - 2025-06-09 21:47:21 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-09 21:47:21 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-09 21:47:21 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-09 21:47:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-09 21:47:24 --> Error: Class "TCPDF" not found
[Method: GET, Route: events/download-certificate/18]
in APPPATH\Controllers\Events.php on line 372.
 1 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->downloadCertificate(18)
 2 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-09 21:47:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:47:32 --> Current Time for Certificate Query: 2025-06-09 21:47:32
DEBUG - 2025-06-09 21:47:32 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-09 21:47:32 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-09 21:47:32 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-09 21:47:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:47:35 --> User ID: 9
DEBUG - 2025-06-09 21:47:35 --> Event ID: 17
DEBUG - 2025-06-09 21:47:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-09 21:47:40 --> Error: Class "TCPDF" not found
[Method: GET, Route: events/download-certificate/17]
in APPPATH\Controllers\Events.php on line 372.
 1 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Events->downloadCertificate(17)
 2 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Events))
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-09 21:47:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:47:42 --> User ID: 9
DEBUG - 2025-06-09 21:47:42 --> Event ID: 17
DEBUG - 2025-06-09 21:47:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:47:44 --> Current Time for Certificate Query: 2025-06-09 21:47:44
DEBUG - 2025-06-09 21:47:44 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-09 21:47:44 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-09 21:47:44 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-09 21:50:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:50:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:50:39 --> User ID: 9
DEBUG - 2025-06-09 21:50:39 --> Event ID: 18
DEBUG - 2025-06-09 21:50:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:51:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:51:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:52:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:52:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:52:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:52:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:52:35 --> Current Time for Certificate Query: 2025-06-09 21:52:35
DEBUG - 2025-06-09 21:52:35 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-09 21:52:35 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-09 21:52:35 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-09 21:52:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 21:54:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 22:06:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 22:06:00 --> Current Time for Certificate Query: 2025-06-09 22:06:00
DEBUG - 2025-06-09 22:06:00 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-09 22:06:00 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-09 22:06:00 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-09 22:06:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 22:06:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 22:06:15 --> User ID: 9
DEBUG - 2025-06-09 22:06:15 --> Event ID: 17
DEBUG - 2025-06-09 22:06:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 22:06:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 22:06:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 22:06:41 --> User ID: 9
DEBUG - 2025-06-09 22:06:41 --> Event ID: 17
DEBUG - 2025-06-09 22:06:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 22:06:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 22:06:46 --> User ID: 9
DEBUG - 2025-06-09 22:06:46 --> Event ID: 18
DEBUG - 2025-06-09 22:08:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-09 22:08:02 --> ParseError: Unclosed '{' on line 9
[Method: GET, Route: events]
in APPPATH\Controllers\Events.php on line 654.
 1 VENDORPATH\composer\ClassLoader.php(427): Composer\Autoload\{closure}('C:\\sertifikat\\backup\\myapp\\vendor\\composer/../../app/Controllers/Events.php')
 2 [internal function]: Composer\Autoload\ClassLoader->loadClass('App\\Controllers\\Events')
 3 SYSTEMPATH\CodeIgniter.php(885): class_exists('\\App\\Controllers\\Events', true)
 4 SYSTEMPATH\CodeIgniter.php(494): CodeIgniter\CodeIgniter->startController()
 5 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 8 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 9 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-09 22:08:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-09 22:08:16 --> ParseError: Unclosed '{' on line 9
[Method: GET, Route: events/certificate/18]
in APPPATH\Controllers\Events.php on line 654.
 1 VENDORPATH\composer\ClassLoader.php(427): Composer\Autoload\{closure}('C:\\sertifikat\\backup\\myapp\\vendor\\composer/../../app/Controllers/Events.php')
 2 [internal function]: Composer\Autoload\ClassLoader->loadClass('App\\Controllers\\Events')
 3 SYSTEMPATH\CodeIgniter.php(885): class_exists('\\App\\Controllers\\Events', true)
 4 SYSTEMPATH\CodeIgniter.php(494): CodeIgniter\CodeIgniter->startController()
 5 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 8 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 9 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-09 22:08:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-09 22:08:19 --> ParseError: Unclosed '{' on line 9
[Method: GET, Route: events]
in APPPATH\Controllers\Events.php on line 654.
 1 VENDORPATH\composer\ClassLoader.php(427): Composer\Autoload\{closure}('C:\\sertifikat\\backup\\myapp\\vendor\\composer/../../app/Controllers/Events.php')
 2 [internal function]: Composer\Autoload\ClassLoader->loadClass('App\\Controllers\\Events')
 3 SYSTEMPATH\CodeIgniter.php(885): class_exists('\\App\\Controllers\\Events', true)
 4 SYSTEMPATH\CodeIgniter.php(494): CodeIgniter\CodeIgniter->startController()
 5 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 8 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 9 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-09 22:08:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-09 22:08:20 --> ParseError: Unclosed '{' on line 9
[Method: GET, Route: events/certificate/17]
in APPPATH\Controllers\Events.php on line 654.
 1 VENDORPATH\composer\ClassLoader.php(427): Composer\Autoload\{closure}('C:\\sertifikat\\backup\\myapp\\vendor\\composer/../../app/Controllers/Events.php')
 2 [internal function]: Composer\Autoload\ClassLoader->loadClass('App\\Controllers\\Events')
 3 SYSTEMPATH\CodeIgniter.php(885): class_exists('\\App\\Controllers\\Events', true)
 4 SYSTEMPATH\CodeIgniter.php(494): CodeIgniter\CodeIgniter->startController()
 5 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 8 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 9 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-09 22:08:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-09 22:08:49 --> ParseError: Unclosed '{' on line 9
[Method: GET, Route: events/certificate/17]
in APPPATH\Controllers\Events.php on line 654.
 1 VENDORPATH\composer\ClassLoader.php(427): Composer\Autoload\{closure}('C:\\sertifikat\\backup\\myapp\\vendor\\composer/../../app/Controllers/Events.php')
 2 [internal function]: Composer\Autoload\ClassLoader->loadClass('App\\Controllers\\Events')
 3 SYSTEMPATH\CodeIgniter.php(885): class_exists('\\App\\Controllers\\Events', true)
 4 SYSTEMPATH\CodeIgniter.php(494): CodeIgniter\CodeIgniter->startController()
 5 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 7 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 8 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 9 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-09 22:08:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 22:08:58 --> User ID: 9
DEBUG - 2025-06-09 22:08:58 --> Event ID: 17
DEBUG - 2025-06-09 22:09:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 22:14:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 22:14:41 --> User ID: 9
DEBUG - 2025-06-09 22:14:41 --> Event ID: 17
DEBUG - 2025-06-09 22:15:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 22:15:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 22:15:29 --> User ID: 9
DEBUG - 2025-06-09 22:15:29 --> Event ID: 17
DEBUG - 2025-06-09 22:15:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 22:15:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 22:15:34 --> User ID: 9
DEBUG - 2025-06-09 22:15:34 --> Event ID: 18
DEBUG - 2025-06-09 22:15:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 22:18:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 22:18:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 22:18:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 22:18:52 --> Current Time for Certificate Query: 2025-06-09 22:18:52
DEBUG - 2025-06-09 22:18:52 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-09 22:18:52 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-09 22:18:52 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-09 22:18:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 22:18:55 --> User ID: 9
DEBUG - 2025-06-09 22:18:55 --> Event ID: 17
DEBUG - 2025-06-09 22:18:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 22:19:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 22:19:17 --> User ID: 9
DEBUG - 2025-06-09 22:19:17 --> Event ID: 17
DEBUG - 2025-06-09 22:19:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 22:19:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 22:19:27 --> User ID: 9
DEBUG - 2025-06-09 22:19:27 --> Event ID: 17
DEBUG - 2025-06-09 22:19:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 22:19:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-09 22:19:44 --> User ID: 9
DEBUG - 2025-06-09 22:19:44 --> Event ID: 17
