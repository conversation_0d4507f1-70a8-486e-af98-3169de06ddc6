{"url": "http://localhost:8080/events/certificate", "method": "GET", "isAJAX": false, "startTime": **********.968459, "totalTime": 90.2, "totalMemory": "7.129", "segmentDuration": 15, "segmentCount": 7, "CI_VERSION": "4.6.1", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.971071, "duration": 0.011275053024291992}, {"name": "Required Before Filters", "component": "Timer", "start": **********.982347, "duration": 0.0050280094146728516}, {"name": "Routing", "component": "Timer", "start": **********.987385, "duration": 0.0019109249114990234}, {"name": "Before Filters", "component": "Timer", "start": **********.989719, "duration": 0.012961149215698242}, {"name": "Controller", "component": "Timer", "start": **********.002686, "duration": 0.****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.002688, "duration": 0.003928184509277344}, {"name": "After Filters", "component": "Timer", "start": **********.058103, "duration": 8.821487426757812e-06}, {"name": "Required After Filters", "component": "Timer", "start": **********.058163, "duration": 0.0005550384521484375}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(1 total Query, 1  unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "0.51 ms", "sql": "<strong>SELECT</strong> `events`.*\n<strong>FROM</strong> `participants`\n<strong>JOIN</strong> `events` <strong>ON</strong> `events`.`id` = `participants`.`event_id`\n<strong>WHERE</strong> `participants`.`user_id` = &#039;9&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:928", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Events.php:375", "function": "        CodeIgniter\\Model->__call()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Events->certificate()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\sertifikat\\backup\\myapp\\public\\index.php"], "function": "        require_once()", "index": " 10    "}], "trace-file": "APPPATH\\Controllers\\Events.php:375", "qid": "88915b1d1810cbdc05e5774f333c1e4b"}]}, "badgeValue": 1, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.022798, "duration": "0.024459"}, {"name": "Query", "component": "Database", "start": **********.047848, "duration": "0.000512", "query": "<strong>SELECT</strong> `events`.*\n<strong>FROM</strong> `participants`\n<strong>JOIN</strong> `events` <strong>ON</strong> `events`.`id` = `participants`.`event_id`\n<strong>WHERE</strong> `participants`.`user_id` = &#039;9&#039;"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": [{"level": "debug", "msg": "Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler' driver."}, {"level": "debug", "msg": "Current Time for Certificate Query: 2025-06-25 17:46:55"}, {"level": "debug", "msg": "SQL Query for All Registered Events: SELECT `events`.*\nFROM `participants`\nJOIN `events` ON `events`.`id` = `participants`.`event_id`\nWHERE `participants`.`user_id` = '9'"}, {"level": "debug", "msg": "Event: <PERSON><PERSON>, EndDate: 2025-06-09 15:25:00, IsCompleted: true"}, {"level": "debug", "msg": "Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true"}, {"level": "debug", "msg": "Event: dgdgdgafafa, EndDate: 2025-06-25 13:00:00, IsCompleted: true"}]}, "badgeValue": null, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 2, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "View: pages/events/certificate_selection.php", "component": "Views", "start": **********.056475, "duration": 0.0009870529174804688}, {"name": "View: layout/main.php", "component": "Views", "start": **********.055251, "duration": 0.002608060836791992}]}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 163 )", "display": {"coreFiles": [{"path": "SYSTEMPATH\\API\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\Autoloader\\Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH\\BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH\\Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH\\Cache\\CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH\\Cache\\CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Cache\\ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH\\CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH\\Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH\\Config\\AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH\\Config\\DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH\\Config\\Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH\\Config\\Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH\\Config\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH\\Cookie\\CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH\\Database\\BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH\\Database\\BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH\\Database\\BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH\\Database\\Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH\\Database\\ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH\\Database\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH\\Database\\Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH\\Database\\QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH\\Database\\ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH\\Debug\\Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH\\Debug\\Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH\\Events\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Filters\\DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH\\Filters\\FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH\\Filters\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Filters\\ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH\\Filters\\PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH\\Filters\\PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH\\HTTP\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH\\HTTP\\Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH\\HTTP\\IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH\\HTTP\\URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH\\HTTP\\UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH\\Helpers\\array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\filesystem_helper.php", "name": "filesystem_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH\\I18n\\Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH\\I18n\\TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\HandlerInterface.php", "name": "HandlerInterface.php"}, {"path": "SYSTEMPATH\\Log\\Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH\\Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH\\Modules\\Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH\\Router\\Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH\\Router\\RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Session\\Session.php", "name": "Session.php"}, {"path": "SYSTEMPATH\\Session\\SessionInterface.php", "name": "SessionInterface.php"}, {"path": "SYSTEMPATH\\Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init.php", "name": "init.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH\\Traits\\ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH\\Validation\\FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH\\View\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\View\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\View\\ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}, {"path": "SYSTEMPATH\\rewrite.php", "name": "rewrite.php"}], "userFiles": [{"path": "APPPATH\\Common.php", "name": "Common.php"}, {"path": "APPPATH\\Config\\App.php", "name": "App.php"}, {"path": "APPPATH\\Config\\Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH\\Config\\Boot\\development.php", "name": "development.php"}, {"path": "APPPATH\\Config\\Cache.php", "name": "Cache.php"}, {"path": "APPPATH\\Config\\Constants.php", "name": "Constants.php"}, {"path": "APPPATH\\Config\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH\\Config\\Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH\\Config\\Database.php", "name": "Database.php"}, {"path": "APPPATH\\Config\\Events.php", "name": "Events.php"}, {"path": "APPPATH\\Config\\Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH\\Config\\Feature.php", "name": "Feature.php"}, {"path": "APPPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "APPPATH\\Config\\Kint.php", "name": "Kint.php"}, {"path": "APPPATH\\Config\\Logger.php", "name": "Logger.php"}, {"path": "APPPATH\\Config\\Modules.php", "name": "Modules.php"}, {"path": "APPPATH\\Config\\Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH\\Config\\Paths.php", "name": "Paths.php"}, {"path": "APPPATH\\Config\\Routes.php", "name": "Routes.php"}, {"path": "APPPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "APPPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "APPPATH\\Config\\Session.php", "name": "Session.php"}, {"path": "APPPATH\\Config\\Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH\\Config\\UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH\\Config\\View.php", "name": "View.php"}, {"path": "APPPATH\\Controllers\\BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH\\Controllers\\Events.php", "name": "Events.php"}, {"path": "APPPATH\\Filters\\AuthFilter.php", "name": "AuthFilter.php"}, {"path": "APPPATH\\Models\\ParticipantModel.php", "name": "ParticipantModel.php"}, {"path": "APPPATH\\Views\\layout\\main.php", "name": "main.php"}, {"path": "APPPATH\\Views\\pages\\events\\certificate_selection.php", "name": "certificate_selection.php"}, {"path": "FCPATH\\index.php", "name": "index.php"}, {"path": "VENDORPATH\\autoload.php", "name": "autoload.php"}, {"path": "VENDORPATH\\composer\\ClassLoader.php", "name": "ClassLoader.php"}, {"path": "VENDORPATH\\composer\\InstalledVersions.php", "name": "InstalledVersions.php"}, {"path": "VENDORPATH\\composer\\autoload_real.php", "name": "autoload_real.php"}, {"path": "VENDORPATH\\composer\\autoload_static.php", "name": "autoload_static.php"}, {"path": "VENDORPATH\\composer\\installed.php", "name": "installed.php"}, {"path": "VENDORPATH\\composer\\platform_check.php", "name": "platform_check.php"}, {"path": "VENDORPATH\\laminas\\laminas-escaper\\src\\Escaper.php", "name": "Escaper.php"}, {"path": "VENDORPATH\\laminas\\laminas-escaper\\src\\EscaperInterface.php", "name": "EscaperInterface.php"}, {"path": "VENDORPATH\\myclabs\\deep-copy\\src\\DeepCopy\\deep_copy.php", "name": "deep_copy.php"}, {"path": "VENDORPATH\\phpunit\\phpunit\\src\\Framework\\Assert\\Functions.php", "name": "Functions.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LogLevel.php", "name": "LogLevel.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "VENDORPATH\\symfony\\deprecation-contracts\\function.php", "name": "function.php"}]}, "badgeValue": 163, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Events", "method": "certificate", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "login", "handler": "\\App\\Controllers\\Auth::login"}, {"method": "GET", "route": "register", "handler": "\\App\\Controllers\\Auth::register"}, {"method": "GET", "route": "logout", "handler": "\\App\\Controllers\\Auth::logout"}, {"method": "GET", "route": "auth/login", "handler": "\\App\\Controllers\\Auth::login"}, {"method": "GET", "route": "auth/register", "handler": "\\App\\Controllers\\Auth::register"}, {"method": "GET", "route": "auth/logout", "handler": "\\App\\Controllers\\Auth::logout"}, {"method": "GET", "route": "auth/createAdmin", "handler": "\\App\\Controllers\\Auth::createAdmin"}, {"method": "GET", "route": "events", "handler": "\\App\\Controllers\\Events::index"}, {"method": "GET", "route": "events/create", "handler": "\\App\\Controllers\\Events::create"}, {"method": "GET", "route": "events/edit/([0-9]+)", "handler": "\\App\\Controllers\\Events::edit/$1"}, {"method": "GET", "route": "events/delete/([0-9]+)", "handler": "\\App\\Controllers\\Events::delete/$1"}, {"method": "GET", "route": "events/view/([0-9]+)", "handler": "\\App\\Controllers\\Events::view/$1"}, {"method": "GET", "route": "events/participants/([0-9]+)", "handler": "\\App\\Controllers\\Events::participants/$1"}, {"method": "GET", "route": "events/participant_index", "handler": "\\App\\Controllers\\Events::participantIndex"}, {"method": "GET", "route": "events/register/([0-9]+)", "handler": "\\App\\Controllers\\Events::register/$1"}, {"method": "GET", "route": "events/register", "handler": "\\App\\Controllers\\Events::register"}, {"method": "GET", "route": "events/certificate", "handler": "\\App\\Controllers\\Events::certificate"}, {"method": "GET", "route": "events/certificate/([0-9]+)", "handler": "\\App\\Controllers\\Events::certificate/$1"}, {"method": "GET", "route": "events/download-certificate/([0-9]+)", "handler": "\\App\\Controllers\\Events::downloadCertificate/$1"}, {"method": "GET", "route": "admin", "handler": "\\App\\Controllers\\Admin::index"}, {"method": "GET", "route": "admin/users", "handler": "\\App\\Controllers\\Admin::users"}, {"method": "GET", "route": "admin/events", "handler": "\\App\\Controllers\\Admin::events"}, {"method": "GET", "route": "participants/unregister/([0-9]+)", "handler": "\\App\\Controllers\\Participants::unregister/$1"}, {"method": "GET", "route": "dashboard", "handler": "\\App\\Controllers\\Dashboard::index"}, {"method": "GET", "route": "reports", "handler": "\\App\\Controllers\\Reports::index"}, {"method": "GET", "route": "reports/dashboard", "handler": "\\App\\Controllers\\Reports::index"}, {"method": "GET", "route": "reports/events", "handler": "\\App\\Controllers\\Reports::events"}, {"method": "GET", "route": "reports/participants", "handler": "\\App\\Controllers\\Reports::participants"}, {"method": "GET", "route": "reports/certificates", "handler": "\\App\\Controllers\\Reports::certificates"}, {"method": "GET", "route": "reports/export/events/excel", "handler": "\\App\\Controllers\\Reports::exportEventsExcel"}, {"method": "GET", "route": "reports/export/events/pdf", "handler": "\\App\\Controllers\\Reports::exportEventsPdf"}, {"method": "GET", "route": "reports/export/participants/excel", "handler": "\\App\\Controllers\\Reports::exportParticipantsExcel"}, {"method": "GET", "route": "reports/export/participants/pdf", "handler": "\\App\\Controllers\\Reports::exportParticipantsPdf"}, {"method": "GET", "route": "reports/export/certificates/excel", "handler": "\\App\\Controllers\\Reports::exportCertificatesExcel"}, {"method": "GET", "route": "reports/export/certificates/pdf", "handler": "\\App\\Controllers\\Reports::exportCertificatesPdf"}, {"method": "GET", "route": "certificates/generate/([0-9]+)", "handler": "\\App\\Controllers\\Certificates::generate/$1"}, {"method": "GET", "route": "profile", "handler": "\\App\\Controllers\\Profile::index"}, {"method": "POST", "route": "login", "handler": "\\App\\Controllers\\Auth::attemptLogin"}, {"method": "POST", "route": "register", "handler": "\\App\\Controllers\\Auth::attemptRegister"}, {"method": "POST", "route": "auth/attemptLogin", "handler": "\\App\\Controllers\\Auth::attemptLogin"}, {"method": "POST", "route": "auth/attemptRegister", "handler": "\\App\\Controllers\\Auth::attemptRegister"}, {"method": "POST", "route": "auth/createAdmin", "handler": "\\App\\Controllers\\Auth::createAdmin"}, {"method": "POST", "route": "events/store", "handler": "\\App\\Controllers\\Events::store"}, {"method": "POST", "route": "events/update/([0-9]+)", "handler": "\\App\\Controllers\\Events::update/$1"}, {"method": "POST", "route": "events/preview-certificate", "handler": "\\App\\Controllers\\Events::previewCertificate"}, {"method": "POST", "route": "participants/register", "handler": "\\App\\Controllers\\Participants::register"}, {"method": "POST", "route": "profile/update", "handler": "\\App\\Controllers\\Profile::update"}, {"method": "POST", "route": "profile/change-password", "handler": "\\App\\Controllers\\Profile::changePassword"}]}, "badgeValue": 39, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "4.93", "count": 1}, "DBQuery": {"event": "DB<PERSON>uery", "duration": "0.03", "count": 1}}}, "badgeValue": 2, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.977414, "duration": 0.004929065704345703}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.048379, "duration": 3.314018249511719e-05}]}], "vars": {"varData": {"View Data": {"title": "Download Sertifikat", "page": "events/certificate_selection", "events": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (3)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (3)</li><li>Contents (3)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>title</th><th>description</th><th>start_date</th><th>end_date</th><th>location</th><th>max_participants</th><th>created_at</th><th>updated_at</th><th>institution_name</th><th>certificate_number</th><th>organizer_name</th><th>organizer_role</th><th>institution_logo</th><th>organizer_signature</th><th>is_completed</th></tr></thead><tbody><tr><th>0</th><td title=\"string (2)\">17</td><td title=\"string (12)\">Lamine yamal</td><td title=\"string (12)\">Lamine yamal</td><td title=\"string (19)\">2025-06-09 15:23:00</td><td title=\"string (19)\">2025-06-09 15:25:00</td><td title=\"string (7)\">Bandung</td><td title=\"string (1)\">2</td><td title=\"string (19)\">2025-06-09 15:23:51</td><td title=\"string (19)\">2025-06-09 15:23:51</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"boolean\"><var>true</var></td></tr><tr><th>1</th><td title=\"string (2)\">18</td><td title=\"string (26)\">seminar bareng lamine fati</td><td title=\"string (26)\">seminar bareng lamine fati</td><td title=\"string (19)\">2025-06-09 21:24:00</td><td title=\"string (19)\">2025-06-09 21:26:00</td><td title=\"string (7)\">Bandung</td><td title=\"string (1)\">2</td><td title=\"string (19)\">2025-06-09 21:25:03</td><td title=\"string (19)\">2025-06-09 21:25:03</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"boolean\"><var>true</var></td></tr><tr><th>2</th><td title=\"string (2)\">21</td><td title=\"string (11)\">dgdgdgafafa</td><td title=\"string (18)\">afafafafafafafafaf</td><td title=\"string (19)\">2025-06-25 12:58:00</td><td title=\"string (19)\">2025-06-25 13:00:00</td><td title=\"string (8)\">Cikarang</td><td title=\"string (1)\">2</td><td title=\"string (19)\">2025-06-25 12:59:33</td><td title=\"string (19)\">2025-06-25 12:59:33</td><td title=\"string (19)\">Universitas Binance</td><td title=\"string (13)\">001/CERT/2024</td><td title=\"string (11)\">Ridho Fauzi</td><td title=\"string (9)\">Pembicara</td><td title=\"string (35)\">1750831173_d54c55cf716f4107aae0.jpg</td><td title=\"string (35)\">1750831173_9b869c690bc3027b7510.png</td><td title=\"boolean\"><var>true</var></td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"17\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (12) \"Lamine yamal\"<div class=\"access-path\">$value[0]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (12) \"Lamine yamal\"<div class=\"access-path\">$value[0]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_date</dfn> =&gt; <var>string</var> (19) \"2025-06-09 15:23:00\"<div class=\"access-path\">$value[0]['start_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_date</dfn> =&gt; <var>string</var> (19) \"2025-06-09 15:25:00\"<div class=\"access-path\">$value[0]['end_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>location</dfn> =&gt; <var>string</var> (7) \"Bandung\"<div class=\"access-path\">$value[0]['location']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_participants</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[0]['max_participants']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-09 15:23:51\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-09 15:23:51\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['institution_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>certificate_number</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['certificate_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['organizer_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_role</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['organizer_role']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_logo</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['institution_logo']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_signature</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['organizer_signature']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_completed</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[0]['is_completed']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"18\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (26) \"seminar bareng lamine fati\"<div class=\"access-path\">$value[1]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (26) \"seminar bareng lamine fati\"<div class=\"access-path\">$value[1]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_date</dfn> =&gt; <var>string</var> (19) \"2025-06-09 21:24:00\"<div class=\"access-path\">$value[1]['start_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_date</dfn> =&gt; <var>string</var> (19) \"2025-06-09 21:26:00\"<div class=\"access-path\">$value[1]['end_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>location</dfn> =&gt; <var>string</var> (7) \"Bandung\"<div class=\"access-path\">$value[1]['location']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_participants</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['max_participants']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-09 21:25:03\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-09 21:25:03\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['institution_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>certificate_number</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['certificate_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['organizer_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_role</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['organizer_role']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_logo</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['institution_logo']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_signature</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['organizer_signature']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_completed</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[1]['is_completed']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"21\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (11) \"dgdgdgafafa\"<div class=\"access-path\">$value[2]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (18) \"afafafafafafafafaf\"<div class=\"access-path\">$value[2]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_date</dfn> =&gt; <var>string</var> (19) \"2025-06-25 12:58:00\"<div class=\"access-path\">$value[2]['start_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_date</dfn> =&gt; <var>string</var> (19) \"2025-06-25 13:00:00\"<div class=\"access-path\">$value[2]['end_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>location</dfn> =&gt; <var>string</var> (8) \"Cikarang\"<div class=\"access-path\">$value[2]['location']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_participants</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[2]['max_participants']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-25 12:59:33\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-25 12:59:33\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_name</dfn> =&gt; <var>string</var> (19) \"Universitas Binance\"<div class=\"access-path\">$value[2]['institution_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>certificate_number</dfn> =&gt; <var>string</var> (13) \"001/CERT/2024\"<div class=\"access-path\">$value[2]['certificate_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_name</dfn> =&gt; <var>string</var> (11) \"Ridho Fauzi\"<div class=\"access-path\">$value[2]['organizer_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_role</dfn> =&gt; <var>string</var> (9) \"Pembicara\"<div class=\"access-path\">$value[2]['organizer_role']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_logo</dfn> =&gt; <var>string</var> (35) \"1750831173_d54c55cf716f4107aae0.jpg\"<div class=\"access-path\">$value[2]['institution_logo']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_signature</dfn> =&gt; <var>string</var> (35) \"1750831173_9b869c690bc3027b7510.png\"<div class=\"access-path\">$value[2]['organizer_signature']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>is_completed</dfn> =&gt; <var>boolean</var> true<div class=\"access-path\">$value[2]['is_completed']</div></dt></dl></dd></dl></li></ul></dd></dl></div>"}}, "session": {"__ci_last_regenerate": "<pre>1750848215</pre>", "_ci_previous_url": "http://localhost:8080/events/certificate", "id": "9", "email": "<EMAIL>", "name": "lamine yamal", "phone_number": "0838184614786", "role": "participant", "isLoggedIn": "<pre>1</pre>", "__ci_vars": "<pre>Array\n(\n)\n</pre>"}, "headers": {"Host": "localhost:8080", "Connection": "keep-alive", "Sec-Ch-Ua": "&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "&quot;Windows&quot;", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-User": "?1", "Sec-Fetch-Dest": "document", "Referer": "http://localhost:8080/dashboard", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "en-US,en;q=0.9", "Cookie": "csrf_cookie_name=fb8a9c490ca47ec5488f3d57a758c511; ci_session=f18bb7ad70b93f1b8089af2fb3aa3fdc"}, "cookies": {"csrf_cookie_name": "fb8a9c490ca47ec5488f3d57a758c511", "ci_session": "f18bb7ad70b93f1b8089af2fb3aa3fdc"}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.6.1", "phpVersion": "8.2.12", "phpSAPI": "cli-server", "environment": "development", "baseURL": "http://localhost:8080", "timezone": "Asia/Jakarta", "locale": "en", "cspEnabled": false}}