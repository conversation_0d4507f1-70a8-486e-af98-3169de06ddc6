DEBUG - 2025-06-23 20:05:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 20:05:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-23 20:06:03 --> Error connecting to the database: mysqli_sql_exception: No connection could be made because the target machine actively refused it in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'event_managemen...', 3306, '', 0)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#5 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\BaseModel.php(717): CodeIgniter\Model->doFirst()
#6 C:\sertifikat\backup\myapp\app\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
#7 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#11 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 C:\sertifikat\backup\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\b...')
#14 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: No connection could be made because the target machine actively refused it in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#4 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\BaseModel.php(717): CodeIgniter\Model->doFirst()
#5 C:\sertifikat\backup\myapp\app\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
#6 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
#7 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 C:\sertifikat\backup\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\b...')
#13 {main}
CRITICAL - 2025-06-23 20:06:03 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: No connection could be made because the target machine actively refused it
[Method: POST, Route: auth/attemptLogin]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 3 SYSTEMPATH\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(717): CodeIgniter\Model->doFirst()
 5 APPPATH\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-23 20:06:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 20:06:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 20:06:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 20:06:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 20:06:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 20:06:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 20:07:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 20:07:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 20:07:47 --> Current Time for Certificate Query: 2025-06-23 20:07:47
DEBUG - 2025-06-23 20:07:47 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-23 20:07:47 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-23 20:07:47 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-23 20:08:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 20:09:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 20:09:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 20:09:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 20:09:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 20:09:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 20:09:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 20:10:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 20:10:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 20:10:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 20:10:30 --> Current Time for Certificate Query: 2025-06-23 20:10:30
DEBUG - 2025-06-23 20:10:30 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-23 20:10:30 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-23 20:10:30 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-23 20:10:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 20:10:32 --> User ID: 9
DEBUG - 2025-06-23 20:10:32 --> Event ID: 17
DEBUG - 2025-06-23 20:10:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-23 20:10:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
