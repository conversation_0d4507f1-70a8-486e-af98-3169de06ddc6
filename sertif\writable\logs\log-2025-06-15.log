DEBUG - 2025-06-15 20:35:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:35:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
ERROR - 2025-06-15 20:36:22 --> Error connecting to the database: mysqli_sql_exception: No connection could be made because the target machine actively refused it in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', 'root', Object(SensitiveParameterValue), 'event_managemen...', 3306, '', 0)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#5 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\BaseModel.php(717): CodeIgniter\Model->doFirst()
#6 C:\sertifikat\backup\myapp\app\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
#7 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#11 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 C:\sertifikat\backup\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\b...')
#14 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: No connection could be made because the target machine actively refused it in C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
#4 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\BaseModel.php(717): CodeIgniter\Model->doFirst()
#5 C:\sertifikat\backup\myapp\app\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
#6 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
#7 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
#8 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#10 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 C:\sertifikat\backup\myapp\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 C:\sertifikat\backup\myapp\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\sertifikat\\b...')
#13 {main}
CRITICAL - 2025-06-15 20:36:22 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: No connection could be made because the target machine actively refused it
[Method: POST, Route: auth/attemptLogin]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `users`
WHERE `email` = :email:
 LIMIT 1', [...], false)
 3 SYSTEMPATH\Model.php(330): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH\BaseModel.php(717): CodeIgniter\Model->doFirst()
 5 APPPATH\Controllers\Auth.php(21): CodeIgniter\BaseModel->first()
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->attemptLogin()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-15 20:36:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:36:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:36:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:36:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:36:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:37:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:37:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:37:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:38:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:38:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:39:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:39:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:39:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:39:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:39:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:39:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:39:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:39:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:39:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:39:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:39:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:40:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:40:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:40:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-15 20:40:53 --> ErrorException: Undefined array key "organizer_name"
[Method: GET, Route: certificates/generate/18]
in APPPATH\Views\pages\certificates\generate.php on line 39.
 1 APPPATH\Views\pages\certificates\generate.php(39): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "organizer_name"', 'C:\\sertifikat\\backup\\myapp\\app\\Views\\pages\\certificates\\generate.php', 39)
 2 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\backup\\myapp\\app\\Views\\pages\\certificates\\generate.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/certificates/generate', [], true)
 5 APPPATH\Controllers\Certificates.php(46): view('pages/certificates/generate', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Certificates->generate('18')
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Certificates))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-15 20:42:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:42:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:43:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:43:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:43:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:43:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:43:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:43:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:43:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:43:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:43:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:44:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:44:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:44:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:44:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:44:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:44:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:44:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:45:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:47:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:47:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:47:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:47:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:47:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:47:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:53:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:55:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:55:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:55:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:55:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:55:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 20:55:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 21:03:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 21:04:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 21:04:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 21:21:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 21:21:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 21:23:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 21:23:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 21:23:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 21:23:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 21:54:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 21:55:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 21:55:00 --> Certificate Generate Data: {"title":"Sertifikat - Seminar Bareng Alvin alfandy","event":{"id":"14","title":"Seminar Bareng Alvin alfandy","description":"Seminar Bareng Alvin alfandy","start_date":"2025-06-03 10:56:00","end_date":"2025-06-03 10:58:00","location":"surabaya","max_participants":"2","created_at":"2025-06-03 10:56:11","updated_at":"2025-06-03 10:56:11"},"user":{"id":"8","email":"<EMAIL>","name":"abidzar sabil","phone_number":"08515614128741789","password":"$2y$10$sm68j61g7RXavjqJTPeJUeJVa1H\/kI\/NIonBUz21OvTSGBRrptSwa","role":"participant","created_at":null,"updated_at":null},"participant":{"id":"17","event_id":"14","user_id":"8","name":"abidzar sabil","email":"<EMAIL>","phone":"08515614128741789","created_at":"2025-06-03 10:56:17","updated_at":"2025-06-03 10:56:17"}}
DEBUG - 2025-06-15 21:55:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 21:55:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 21:55:06 --> Certificate Generate Data: {"title":"Sertifikat - Seminar Bareng Alvin alfandy","event":{"id":"14","title":"Seminar Bareng Alvin alfandy","description":"Seminar Bareng Alvin alfandy","start_date":"2025-06-03 10:56:00","end_date":"2025-06-03 10:58:00","location":"surabaya","max_participants":"2","created_at":"2025-06-03 10:56:11","updated_at":"2025-06-03 10:56:11"},"user":{"id":"8","email":"<EMAIL>","name":"abidzar sabil","phone_number":"08515614128741789","password":"$2y$10$sm68j61g7RXavjqJTPeJUeJVa1H\/kI\/NIonBUz21OvTSGBRrptSwa","role":"participant","created_at":null,"updated_at":null},"participant":{"id":"17","event_id":"14","user_id":"8","name":"abidzar sabil","email":"<EMAIL>","phone":"08515614128741789","created_at":"2025-06-03 10:56:17","updated_at":"2025-06-03 10:56:17"}}
DEBUG - 2025-06-15 21:56:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 21:56:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-15 21:56:49 --> ErrorException: Undefined array key "organizer_name"
[Method: GET, Route: certificates/generate/17]
in APPPATH\Views\pages\certificates\generate.php on line 39.
 1 APPPATH\Views\pages\certificates\generate.php(39): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "organizer_name"', 'C:\\sertifikat\\backup\\myapp\\app\\Views\\pages\\certificates\\generate.php', 39)
 2 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\backup\\myapp\\app\\Views\\pages\\certificates\\generate.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/certificates/generate', [], true)
 5 APPPATH\Controllers\Certificates.php(46): view('pages/certificates/generate', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Certificates->generate('17')
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Certificates))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-15 21:58:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-15 21:58:30 --> ErrorException: Undefined array key "organizer_name"
[Method: GET, Route: certificates/generate/17]
in APPPATH\Views\pages\certificates\generate.php on line 39.
 1 APPPATH\Views\pages\certificates\generate.php(39): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "organizer_name"', 'C:\\sertifikat\\backup\\myapp\\app\\Views\\pages\\certificates\\generate.php', 39)
 2 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\backup\\myapp\\app\\Views\\pages\\certificates\\generate.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/certificates/generate', [], true)
 5 APPPATH\Controllers\Certificates.php(46): view('pages/certificates/generate', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Certificates->generate('17')
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Certificates))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-15 21:58:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 21:58:36 --> Current Time for Certificate Query: 2025-06-15 21:58:36
DEBUG - 2025-06-15 21:58:36 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-15 21:58:36 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-15 21:58:36 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-15 21:58:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 21:58:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 21:58:45 --> User ID: 9
DEBUG - 2025-06-15 21:58:45 --> Event ID: 17
DEBUG - 2025-06-15 22:04:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 22:04:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-15 22:04:31 --> ErrorException: Undefined array key "organizer_name"
[Method: GET, Route: certificates/generate/22]
in APPPATH\Views\pages\certificates\generate.php on line 39.
 1 APPPATH\Views\pages\certificates\generate.php(39): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "organizer_name"', 'C:\\sertifikat\\backup\\myapp\\app\\Views\\pages\\certificates\\generate.php', 39)
 2 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\backup\\myapp\\app\\Views\\pages\\certificates\\generate.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/certificates/generate', [], true)
 5 APPPATH\Controllers\Certificates.php(46): view('pages/certificates/generate', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Certificates->generate('22')
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Certificates))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-15 22:04:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 22:15:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 22:18:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 22:18:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 22:18:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 22:18:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 22:18:23 --> User ID: 9
DEBUG - 2025-06-15 22:18:23 --> Event ID: 17
DEBUG - 2025-06-15 22:18:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 22:18:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 22:18:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 22:18:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 22:19:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 22:19:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 22:20:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 22:20:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 22:20:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 22:23:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-15 22:23:28 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "pages/pages/auth/create_admin.php"
[Method: GET, Route: auth/createAdmin]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('pages/pages/auth/create_admin.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/pages/auth/create_admin', [], true)
 3 APPPATH\Views\layout\main.php(281): view('pages/pages/auth/create_admin')
 4 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\backup\\myapp\\app\\Views\\layout\\main.php')
 5 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 6 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('layout/main', [], true)
 7 APPPATH\Controllers\Auth.php(128): view('layout/main', [...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->createAdmin()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-15 23:24:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-15 23:24:18 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "pages/pages/auth/create_admin.php"
[Method: GET, Route: auth/createAdmin]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('pages/pages/auth/create_admin.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/pages/auth/create_admin', [], true)
 3 APPPATH\Views\layout\main.php(281): view('pages/pages/auth/create_admin')
 4 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\backup\\myapp\\app\\Views\\layout\\main.php')
 5 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 6 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('layout/main', [], true)
 7 APPPATH\Controllers\Auth.php(128): view('layout/main', [...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->createAdmin()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-15 23:24:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-15 23:24:31 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "pages/pages/auth/create_admin.php"
[Method: GET, Route: auth/createAdmin]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('pages/pages/auth/create_admin.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/pages/auth/create_admin', [], true)
 3 APPPATH\Views\layout\main.php(281): view('pages/pages/auth/create_admin')
 4 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\backup\\myapp\\app\\Views\\layout\\main.php')
 5 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 6 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('layout/main', [], true)
 7 APPPATH\Controllers\Auth.php(128): view('layout/main', [...])
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Auth->createAdmin()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Auth))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-15 23:24:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
