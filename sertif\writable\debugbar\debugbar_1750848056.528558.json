{"url": "http://localhost:8080/events", "method": "GET", "isAJAX": false, "startTime": **********.391071, "totalTime": 68.8, "totalMemory": "7.151", "segmentDuration": 10, "segmentCount": 7, "CI_VERSION": "4.6.1", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.393696, "duration": 0.011528968811035156}, {"name": "Required Before Filters", "component": "Timer", "start": **********.405228, "duration": 0.0031731128692626953}, {"name": "Routing", "component": "Timer", "start": **********.408406, "duration": 0.0012428760528564453}, {"name": "Before Filters", "component": "Timer", "start": **********.410021, "duration": 0.011267900466918945}, {"name": "Controller", "component": "Timer", "start": **********.421295, "duration": 0.*****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.421297, "duration": 0.002974987030029297}, {"name": "After Filters", "component": "Timer", "start": **********.458959, "duration": 8.821487426757812e-06}, {"name": "Required After Filters", "component": "Timer", "start": **********.459024, "duration": 0.000965118408203125}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(17 total Queries, 17 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "0.33 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `events`", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\Events.php:25", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Events->index()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\sertifikat\\backup\\myapp\\public\\index.php"], "function": "        require_once()", "index": " 11    "}], "trace-file": "APPPATH\\Controllers\\Events.php:25", "qid": "09ae01a1ccd2dfa208600b849ab3f4fa"}, {"hover": "", "class": "", "duration": "0.23 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `participants`\n<strong>WHERE</strong> `event_id` = &#039;5&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Events.php:29", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Events->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\sertifikat\\backup\\myapp\\public\\index.php"], "function": "        require_once()", "index": " 10    "}], "trace-file": "APPPATH\\Controllers\\Events.php:29", "qid": "2dc41922fbd4dd7b273ef9fa485cb02b"}, {"hover": "", "class": "", "duration": "0.17 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `participants`\n<strong>WHERE</strong> `event_id` = &#039;6&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Events.php:29", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Events->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\sertifikat\\backup\\myapp\\public\\index.php"], "function": "        require_once()", "index": " 10    "}], "trace-file": "APPPATH\\Controllers\\Events.php:29", "qid": "f4d344cf4edc567627a409e0ec7c6e41"}, {"hover": "", "class": "", "duration": "0.22 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `participants`\n<strong>WHERE</strong> `event_id` = &#039;7&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Events.php:29", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Events->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\sertifikat\\backup\\myapp\\public\\index.php"], "function": "        require_once()", "index": " 10    "}], "trace-file": "APPPATH\\Controllers\\Events.php:29", "qid": "e8422f577abc81cce7fc67fda1acdee7"}, {"hover": "", "class": "", "duration": "0.13 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `participants`\n<strong>WHERE</strong> `event_id` = &#039;8&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Events.php:29", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Events->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\sertifikat\\backup\\myapp\\public\\index.php"], "function": "        require_once()", "index": " 10    "}], "trace-file": "APPPATH\\Controllers\\Events.php:29", "qid": "a2d96eb051b62e9145eb66bd3ae0ae38"}, {"hover": "", "class": "", "duration": "0.11 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `participants`\n<strong>WHERE</strong> `event_id` = &#039;9&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Events.php:29", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Events->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\sertifikat\\backup\\myapp\\public\\index.php"], "function": "        require_once()", "index": " 10    "}], "trace-file": "APPPATH\\Controllers\\Events.php:29", "qid": "af39c142356df742bd8ffceebe1b98a6"}, {"hover": "", "class": "", "duration": "0.11 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `participants`\n<strong>WHERE</strong> `event_id` = &#039;10&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Events.php:29", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Events->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\sertifikat\\backup\\myapp\\public\\index.php"], "function": "        require_once()", "index": " 10    "}], "trace-file": "APPPATH\\Controllers\\Events.php:29", "qid": "3f03a62eec9e6270f11608112efe8f3d"}, {"hover": "", "class": "", "duration": "0.11 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `participants`\n<strong>WHERE</strong> `event_id` = &#039;11&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Events.php:29", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Events->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\sertifikat\\backup\\myapp\\public\\index.php"], "function": "        require_once()", "index": " 10    "}], "trace-file": "APPPATH\\Controllers\\Events.php:29", "qid": "a510d8fc9a52b02b79896718659647ac"}, {"hover": "", "class": "", "duration": "0.1 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `participants`\n<strong>WHERE</strong> `event_id` = &#039;12&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Events.php:29", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Events->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\sertifikat\\backup\\myapp\\public\\index.php"], "function": "        require_once()", "index": " 10    "}], "trace-file": "APPPATH\\Controllers\\Events.php:29", "qid": "50dbdc2085bc58bef8376fdc3f1a7f98"}, {"hover": "", "class": "", "duration": "0.11 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `participants`\n<strong>WHERE</strong> `event_id` = &#039;13&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Events.php:29", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Events->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\sertifikat\\backup\\myapp\\public\\index.php"], "function": "        require_once()", "index": " 10    "}], "trace-file": "APPPATH\\Controllers\\Events.php:29", "qid": "18208b1dad5bdbbae2cddefb6ed14f6b"}, {"hover": "", "class": "", "duration": "0.14 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `participants`\n<strong>WHERE</strong> `event_id` = &#039;14&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Events.php:29", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Events->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\sertifikat\\backup\\myapp\\public\\index.php"], "function": "        require_once()", "index": " 10    "}], "trace-file": "APPPATH\\Controllers\\Events.php:29", "qid": "bc166e13ca59461f0efb86de1dc48ea3"}, {"hover": "", "class": "", "duration": "0.24 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `participants`\n<strong>WHERE</strong> `event_id` = &#039;15&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Events.php:29", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Events->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\sertifikat\\backup\\myapp\\public\\index.php"], "function": "        require_once()", "index": " 10    "}], "trace-file": "APPPATH\\Controllers\\Events.php:29", "qid": "6414b405cc8d2c1630bdc9407639de88"}, {"hover": "", "class": "", "duration": "0.16 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `participants`\n<strong>WHERE</strong> `event_id` = &#039;16&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Events.php:29", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Events->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\sertifikat\\backup\\myapp\\public\\index.php"], "function": "        require_once()", "index": " 10    "}], "trace-file": "APPPATH\\Controllers\\Events.php:29", "qid": "01910c62c5803e88a7324159249d5fb1"}, {"hover": "", "class": "", "duration": "0.15 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `participants`\n<strong>WHERE</strong> `event_id` = &#039;17&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Events.php:29", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Events->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\sertifikat\\backup\\myapp\\public\\index.php"], "function": "        require_once()", "index": " 10    "}], "trace-file": "APPPATH\\Controllers\\Events.php:29", "qid": "027c1db7efbc6c027a80144845ff1a27"}, {"hover": "", "class": "", "duration": "0.21 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `participants`\n<strong>WHERE</strong> `event_id` = &#039;18&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Events.php:29", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Events->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\sertifikat\\backup\\myapp\\public\\index.php"], "function": "        require_once()", "index": " 10    "}], "trace-file": "APPPATH\\Controllers\\Events.php:29", "qid": "1757833efdff434718ff1619cb4919ef"}, {"hover": "", "class": "", "duration": "0.12 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `participants`\n<strong>WHERE</strong> `event_id` = &#039;19&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Events.php:29", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Events->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\sertifikat\\backup\\myapp\\public\\index.php"], "function": "        require_once()", "index": " 10    "}], "trace-file": "APPPATH\\Controllers\\Events.php:29", "qid": "e9fa5ca08ab19c93a6eb84f3ea35ae4c"}, {"hover": "", "class": "", "duration": "0.1 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `participants`\n<strong>WHERE</strong> `event_id` = &#039;21&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\Events.php:29", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Events->index()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\sertifikat\\backup\\myapp\\public\\index.php"], "function": "        require_once()", "index": " 10    "}], "trace-file": "APPPATH\\Controllers\\Events.php:29", "qid": "ca0218033553a2d7f4f9958ebc362a73"}]}, "badgeValue": 17, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.438251, "duration": "0.011283"}, {"name": "Query", "component": "Database", "start": **********.450087, "duration": "0.000332", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `events`"}, {"name": "Query", "component": "Database", "start": **********.451269, "duration": "0.000231", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `participants`\n<strong>WHERE</strong> `event_id` = &#039;5&#039;"}, {"name": "Query", "component": "Database", "start": **********.451564, "duration": "0.000173", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `participants`\n<strong>WHERE</strong> `event_id` = &#039;6&#039;"}, {"name": "Query", "component": "Database", "start": **********.45186, "duration": "0.000222", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `participants`\n<strong>WHERE</strong> `event_id` = &#039;7&#039;"}, {"name": "Query", "component": "Database", "start": **********.452121, "duration": "0.000126", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `participants`\n<strong>WHERE</strong> `event_id` = &#039;8&#039;"}, {"name": "Query", "component": "Database", "start": **********.452281, "duration": "0.000112", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `participants`\n<strong>WHERE</strong> `event_id` = &#039;9&#039;"}, {"name": "Query", "component": "Database", "start": **********.452426, "duration": "0.000111", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `participants`\n<strong>WHERE</strong> `event_id` = &#039;10&#039;"}, {"name": "Query", "component": "Database", "start": **********.452569, "duration": "0.000111", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `participants`\n<strong>WHERE</strong> `event_id` = &#039;11&#039;"}, {"name": "Query", "component": "Database", "start": **********.452711, "duration": "0.000102", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `participants`\n<strong>WHERE</strong> `event_id` = &#039;12&#039;"}, {"name": "Query", "component": "Database", "start": **********.452844, "duration": "0.000105", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `participants`\n<strong>WHERE</strong> `event_id` = &#039;13&#039;"}, {"name": "Query", "component": "Database", "start": **********.452988, "duration": "0.000141", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `participants`\n<strong>WHERE</strong> `event_id` = &#039;14&#039;"}, {"name": "Query", "component": "Database", "start": **********.453234, "duration": "0.000241", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `participants`\n<strong>WHERE</strong> `event_id` = &#039;15&#039;"}, {"name": "Query", "component": "Database", "start": **********.453624, "duration": "0.000165", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `participants`\n<strong>WHERE</strong> `event_id` = &#039;16&#039;"}, {"name": "Query", "component": "Database", "start": **********.453862, "duration": "0.000147", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `participants`\n<strong>WHERE</strong> `event_id` = &#039;17&#039;"}, {"name": "Query", "component": "Database", "start": **********.454063, "duration": "0.000210", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `participants`\n<strong>WHERE</strong> `event_id` = &#039;18&#039;"}, {"name": "Query", "component": "Database", "start": **********.454315, "duration": "0.000118", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `participants`\n<strong>WHERE</strong> `event_id` = &#039;19&#039;"}, {"name": "Query", "component": "Database", "start": **********.454472, "duration": "0.000103", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `participants`\n<strong>WHERE</strong> `event_id` = &#039;21&#039;"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": [{"level": "debug", "msg": "Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler' driver."}]}, "badgeValue": null, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 2, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "View: pages/events/index.php", "component": "Views", "start": **********.455979, "duration": 0.002191781997680664}, {"name": "View: layout/main.php", "component": "Views", "start": **********.455655, "duration": 0.0030548572540283203}]}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 164 )", "display": {"coreFiles": [{"path": "SYSTEMPATH\\API\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\Autoloader\\Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH\\BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH\\Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH\\Cache\\CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH\\Cache\\CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Cache\\ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH\\CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH\\Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH\\Config\\AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH\\Config\\DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH\\Config\\Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH\\Config\\Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH\\Config\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH\\Cookie\\CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH\\Database\\BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH\\Database\\BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH\\Database\\BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH\\Database\\Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH\\Database\\ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH\\Database\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH\\Database\\Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH\\Database\\QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH\\Database\\ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH\\Debug\\Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH\\Debug\\Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH\\Events\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Filters\\DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH\\Filters\\FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH\\Filters\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Filters\\ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH\\Filters\\PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH\\Filters\\PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH\\HTTP\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH\\HTTP\\Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH\\HTTP\\IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH\\HTTP\\URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH\\HTTP\\UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH\\Helpers\\array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\filesystem_helper.php", "name": "filesystem_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH\\I18n\\Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH\\I18n\\TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\HandlerInterface.php", "name": "HandlerInterface.php"}, {"path": "SYSTEMPATH\\Log\\Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH\\Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH\\Modules\\Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH\\Router\\Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH\\Router\\RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Session\\Session.php", "name": "Session.php"}, {"path": "SYSTEMPATH\\Session\\SessionInterface.php", "name": "SessionInterface.php"}, {"path": "SYSTEMPATH\\Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init.php", "name": "init.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH\\Traits\\ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH\\Validation\\FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH\\View\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\View\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\View\\ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}, {"path": "SYSTEMPATH\\rewrite.php", "name": "rewrite.php"}], "userFiles": [{"path": "APPPATH\\Common.php", "name": "Common.php"}, {"path": "APPPATH\\Config\\App.php", "name": "App.php"}, {"path": "APPPATH\\Config\\Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH\\Config\\Boot\\development.php", "name": "development.php"}, {"path": "APPPATH\\Config\\Cache.php", "name": "Cache.php"}, {"path": "APPPATH\\Config\\Constants.php", "name": "Constants.php"}, {"path": "APPPATH\\Config\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH\\Config\\Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH\\Config\\Database.php", "name": "Database.php"}, {"path": "APPPATH\\Config\\Events.php", "name": "Events.php"}, {"path": "APPPATH\\Config\\Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH\\Config\\Feature.php", "name": "Feature.php"}, {"path": "APPPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "APPPATH\\Config\\Kint.php", "name": "Kint.php"}, {"path": "APPPATH\\Config\\Logger.php", "name": "Logger.php"}, {"path": "APPPATH\\Config\\Modules.php", "name": "Modules.php"}, {"path": "APPPATH\\Config\\Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH\\Config\\Paths.php", "name": "Paths.php"}, {"path": "APPPATH\\Config\\Routes.php", "name": "Routes.php"}, {"path": "APPPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "APPPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "APPPATH\\Config\\Session.php", "name": "Session.php"}, {"path": "APPPATH\\Config\\Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH\\Config\\UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH\\Config\\View.php", "name": "View.php"}, {"path": "APPPATH\\Controllers\\BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH\\Controllers\\Events.php", "name": "Events.php"}, {"path": "APPPATH\\Filters\\AuthFilter.php", "name": "AuthFilter.php"}, {"path": "APPPATH\\Models\\EventModel.php", "name": "EventModel.php"}, {"path": "APPPATH\\Models\\ParticipantModel.php", "name": "ParticipantModel.php"}, {"path": "APPPATH\\Views\\layout\\main.php", "name": "main.php"}, {"path": "APPPATH\\Views\\pages\\events\\index.php", "name": "index.php"}, {"path": "FCPATH\\index.php", "name": "index.php"}, {"path": "VENDORPATH\\autoload.php", "name": "autoload.php"}, {"path": "VENDORPATH\\composer\\ClassLoader.php", "name": "ClassLoader.php"}, {"path": "VENDORPATH\\composer\\InstalledVersions.php", "name": "InstalledVersions.php"}, {"path": "VENDORPATH\\composer\\autoload_real.php", "name": "autoload_real.php"}, {"path": "VENDORPATH\\composer\\autoload_static.php", "name": "autoload_static.php"}, {"path": "VENDORPATH\\composer\\installed.php", "name": "installed.php"}, {"path": "VENDORPATH\\composer\\platform_check.php", "name": "platform_check.php"}, {"path": "VENDORPATH\\laminas\\laminas-escaper\\src\\Escaper.php", "name": "Escaper.php"}, {"path": "VENDORPATH\\laminas\\laminas-escaper\\src\\EscaperInterface.php", "name": "EscaperInterface.php"}, {"path": "VENDORPATH\\myclabs\\deep-copy\\src\\DeepCopy\\deep_copy.php", "name": "deep_copy.php"}, {"path": "VENDORPATH\\phpunit\\phpunit\\src\\Framework\\Assert\\Functions.php", "name": "Functions.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LogLevel.php", "name": "LogLevel.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "VENDORPATH\\symfony\\deprecation-contracts\\function.php", "name": "function.php"}]}, "badgeValue": 164, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Events", "method": "index", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "login", "handler": "\\App\\Controllers\\Auth::login"}, {"method": "GET", "route": "register", "handler": "\\App\\Controllers\\Auth::register"}, {"method": "GET", "route": "logout", "handler": "\\App\\Controllers\\Auth::logout"}, {"method": "GET", "route": "auth/login", "handler": "\\App\\Controllers\\Auth::login"}, {"method": "GET", "route": "auth/register", "handler": "\\App\\Controllers\\Auth::register"}, {"method": "GET", "route": "auth/logout", "handler": "\\App\\Controllers\\Auth::logout"}, {"method": "GET", "route": "auth/createAdmin", "handler": "\\App\\Controllers\\Auth::createAdmin"}, {"method": "GET", "route": "events", "handler": "\\App\\Controllers\\Events::index"}, {"method": "GET", "route": "events/create", "handler": "\\App\\Controllers\\Events::create"}, {"method": "GET", "route": "events/edit/([0-9]+)", "handler": "\\App\\Controllers\\Events::edit/$1"}, {"method": "GET", "route": "events/delete/([0-9]+)", "handler": "\\App\\Controllers\\Events::delete/$1"}, {"method": "GET", "route": "events/view/([0-9]+)", "handler": "\\App\\Controllers\\Events::view/$1"}, {"method": "GET", "route": "events/participants/([0-9]+)", "handler": "\\App\\Controllers\\Events::participants/$1"}, {"method": "GET", "route": "events/participant_index", "handler": "\\App\\Controllers\\Events::participantIndex"}, {"method": "GET", "route": "events/register/([0-9]+)", "handler": "\\App\\Controllers\\Events::register/$1"}, {"method": "GET", "route": "events/register", "handler": "\\App\\Controllers\\Events::register"}, {"method": "GET", "route": "events/certificate", "handler": "\\App\\Controllers\\Events::certificate"}, {"method": "GET", "route": "events/certificate/([0-9]+)", "handler": "\\App\\Controllers\\Events::certificate/$1"}, {"method": "GET", "route": "events/download-certificate/([0-9]+)", "handler": "\\App\\Controllers\\Events::downloadCertificate/$1"}, {"method": "GET", "route": "events/previewCertificate/([0-9]+)", "handler": "\\App\\Controllers\\Events::previewCertificateById/$1"}, {"method": "GET", "route": "admin", "handler": "\\App\\Controllers\\Admin::index"}, {"method": "GET", "route": "admin/users", "handler": "\\App\\Controllers\\Admin::users"}, {"method": "GET", "route": "admin/events", "handler": "\\App\\Controllers\\Admin::events"}, {"method": "GET", "route": "participants/unregister/([0-9]+)", "handler": "\\App\\Controllers\\Participants::unregister/$1"}, {"method": "GET", "route": "dashboard", "handler": "\\App\\Controllers\\Dashboard::index"}, {"method": "GET", "route": "reports", "handler": "\\App\\Controllers\\Reports::index"}, {"method": "GET", "route": "reports/dashboard", "handler": "\\App\\Controllers\\Reports::index"}, {"method": "GET", "route": "reports/events", "handler": "\\App\\Controllers\\Reports::events"}, {"method": "GET", "route": "reports/participants", "handler": "\\App\\Controllers\\Reports::participants"}, {"method": "GET", "route": "reports/certificates", "handler": "\\App\\Controllers\\Reports::certificates"}, {"method": "GET", "route": "reports/export/events/excel", "handler": "\\App\\Controllers\\Reports::exportEventsExcel"}, {"method": "GET", "route": "reports/export/events/pdf", "handler": "\\App\\Controllers\\Reports::exportEventsPdf"}, {"method": "GET", "route": "reports/export/participants/excel", "handler": "\\App\\Controllers\\Reports::exportParticipantsExcel"}, {"method": "GET", "route": "reports/export/participants/pdf", "handler": "\\App\\Controllers\\Reports::exportParticipantsPdf"}, {"method": "GET", "route": "reports/export/certificates/excel", "handler": "\\App\\Controllers\\Reports::exportCertificatesExcel"}, {"method": "GET", "route": "reports/export/certificates/pdf", "handler": "\\App\\Controllers\\Reports::exportCertificatesPdf"}, {"method": "GET", "route": "certificates/generate/([0-9]+)", "handler": "\\App\\Controllers\\Certificates::generate/$1"}, {"method": "GET", "route": "profile", "handler": "\\App\\Controllers\\Profile::index"}, {"method": "POST", "route": "login", "handler": "\\App\\Controllers\\Auth::attemptLogin"}, {"method": "POST", "route": "register", "handler": "\\App\\Controllers\\Auth::attemptRegister"}, {"method": "POST", "route": "auth/attemptLogin", "handler": "\\App\\Controllers\\Auth::attemptLogin"}, {"method": "POST", "route": "auth/attemptRegister", "handler": "\\App\\Controllers\\Auth::attemptRegister"}, {"method": "POST", "route": "auth/createAdmin", "handler": "\\App\\Controllers\\Auth::createAdmin"}, {"method": "POST", "route": "events/store", "handler": "\\App\\Controllers\\Events::store"}, {"method": "POST", "route": "events/update/([0-9]+)", "handler": "\\App\\Controllers\\Events::update/$1"}, {"method": "POST", "route": "events/preview-certificate", "handler": "\\App\\Controllers\\Events::previewCertificate"}, {"method": "POST", "route": "participants/register", "handler": "\\App\\Controllers\\Participants::register"}, {"method": "POST", "route": "profile/update", "handler": "\\App\\Controllers\\Profile::update"}, {"method": "POST", "route": "profile/change-password", "handler": "\\App\\Controllers\\Profile::changePassword"}]}, "badgeValue": 40, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "5.34", "count": 1}, "DBQuery": {"event": "DB<PERSON>uery", "duration": "0.12", "count": 17}}}, "badgeValue": 18, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.39988, "duration": 0.005339145660400391}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.450423, "duration": 1.7881393432617188e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.451501, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.45174, "duration": 1.4066696166992188e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.452083, "duration": 3.814697265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.452248, "duration": 3.814697265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.452394, "duration": 4.0531158447265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.452538, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.452681, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.452813, "duration": 4.0531158447265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.45295, "duration": 4.0531158447265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.453132, "duration": 1.2159347534179688e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.453478, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.453791, "duration": 1.2159347534179688e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.45401, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.454274, "duration": 5.9604644775390625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.454434, "duration": 5.0067901611328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.454576, "duration": 5.0067901611328125e-06}]}], "vars": {"varData": {"View Data": {"title": "Kelola Event - BikinEvent.my.id", "page": "events/index", "events": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (16)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (16)</li><li>Contents (16)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>title</th><th>description</th><th>start_date</th><th>end_date</th><th>location</th><th>max_participants</th><th>created_at</th><th>updated_at</th><th>institution_name</th><th>certificate_number</th><th>organizer_name</th><th>organizer_role</th><th>institution_logo</th><th>organizer_signature</th><th>current_participants</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">5</td><td title=\"string (22)\">Seminar Timothy ronald</td><td title=\"string (22)\">Seminar Timothy ronald</td><td title=\"string (19)\">2025-05-31 21:37:00</td><td title=\"string (19)\">2025-05-31 21:41:00</td><td title=\"string (7)\">Jakarta</td><td title=\"string (1)\">2</td><td title=\"string (19)\">2025-05-31 14:35:54</td><td title=\"string (19)\">2025-05-31 14:42:06</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"integer\">1</td></tr><tr><th>1</th><td title=\"string (1)\">6</td><td title=\"string (30)\">seminar baerng marsha lenathea</td><td title=\"string (28)\">http://localhost:8080/events</td><td title=\"string (19)\">2025-06-01 09:31:00</td><td title=\"string (19)\">2025-06-01 09:50:00</td><td title=\"string (8)\">surabaya</td><td title=\"string (1)\">5</td><td title=\"string (19)\">2025-06-01 02:30:15</td><td title=\"string (19)\">2025-06-01 02:30:15</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"integer\">2</td></tr><tr><th>2</th><td title=\"string (1)\">7</td><td title=\"string (22)\">sem9inar bareng marsha</td><td title=\"string (22)\">sem9inar bareng marsha</td><td title=\"string (19)\">2025-06-01 10:57:00</td><td title=\"string (19)\">2025-06-01 12:00:00</td><td title=\"string (7)\">Bandung</td><td title=\"string (1)\">2</td><td title=\"string (19)\">2025-06-01 02:58:14</td><td title=\"string (19)\">2025-06-01 02:58:14</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"integer\">1</td></tr><tr><th>3</th><td title=\"string (1)\">8</td><td title=\"string (18)\">seminar bareng gyj</td><td title=\"string (18)\">seminar bareng gyj</td><td title=\"string (19)\">2025-06-01 20:50:00</td><td title=\"string (19)\">2025-06-01 20:59:00</td><td title=\"string (6)\">Bekasi</td><td title=\"string (1)\">2</td><td title=\"string (19)\">2025-06-01 13:50:02</td><td title=\"string (19)\">2025-06-01 13:50:02</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"integer\">1</td></tr><tr><th>4</th><td title=\"string (1)\">9</td><td title=\"string (19)\">Seminar bareng DIKA</td><td title=\"string (19)\">Seminar bareng DIKA</td><td title=\"string (19)\">2025-06-01 22:06:00</td><td title=\"string (19)\">2025-06-01 22:30:00</td><td title=\"string (7)\">Bandung</td><td title=\"string (2)\">10</td><td title=\"string (19)\">2025-06-01 22:10:52</td><td title=\"string (19)\">2025-06-01 22:10:52</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"integer\">1</td></tr><tr><th>5</th><td title=\"string (2)\">10</td><td title=\"string (32)\">Seminar bareng freya jayawardana</td><td title=\"string (32)\">Seminar bareng freya jayawardana</td><td title=\"string (19)\">2025-06-03 10:42:00</td><td title=\"string (19)\">2025-06-03 10:49:00</td><td title=\"string (7)\">Bandung</td><td title=\"string (1)\">2</td><td title=\"string (19)\">2025-06-03 10:48:03</td><td title=\"string (19)\">2025-06-03 10:48:03</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"integer\">1</td></tr><tr><th>6</th><td title=\"string (2)\">11</td><td title=\"string (20)\">Seminar Bareng Oline</td><td title=\"string (20)\">Seminar Bareng Oline</td><td title=\"string (19)\">2025-06-03 10:50:00</td><td title=\"string (19)\">2025-06-03 10:53:00</td><td title=\"string (7)\">Bandung</td><td title=\"string (1)\">2</td><td title=\"string (19)\">2025-06-03 10:51:09</td><td title=\"string (19)\">2025-06-03 10:51:09</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"integer\">1</td></tr><tr><th>7</th><td title=\"string (2)\">12</td><td title=\"string (48)\">Sertifikat hanya tersedia setelah event selesai.</td><td title=\"string (48)\">Sertifikat hanya tersedia setelah event selesai.</td><td title=\"string (19)\">2025-06-03 10:51:00</td><td title=\"string (19)\">2025-06-03 10:53:00</td><td title=\"string (7)\">Bandung</td><td title=\"string (1)\">2</td><td title=\"string (19)\">2025-06-03 10:51:53</td><td title=\"string (19)\">2025-06-03 10:51:53</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"integer\">1</td></tr><tr><th>8</th><td title=\"string (2)\">13</td><td title=\"string (27)\">Seminar Bareng Shani Indira</td><td title=\"string (27)\">Seminar Bareng Shani Indira</td><td title=\"string (19)\">2025-06-03 10:53:00</td><td title=\"string (19)\">2025-06-03 10:55:00</td><td title=\"string (7)\">Bandung</td><td title=\"string (1)\">2</td><td title=\"string (19)\">2025-06-03 10:53:46</td><td title=\"string (19)\">2025-06-03 10:53:46</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"integer\">1</td></tr><tr><th>9</th><td title=\"string (2)\">14</td><td title=\"string (28)\">Seminar Bareng Alvin alfandy</td><td title=\"string (28)\">Seminar Bareng Alvin alfandy</td><td title=\"string (19)\">2025-06-03 10:56:00</td><td title=\"string (19)\">2025-06-03 10:58:00</td><td title=\"string (8)\">surabaya</td><td title=\"string (1)\">2</td><td title=\"string (19)\">2025-06-03 10:56:11</td><td title=\"string (19)\">2025-06-03 10:56:11</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"integer\">1</td></tr><tr><th>10</th><td title=\"string (2)\">15</td><td title=\"string (37)\">Seminar Bareng Marsha Lenathea Lapian</td><td title=\"string (37)\">Seminar Bareng Marsha Lenathea Lapian</td><td title=\"string (19)\">2025-06-03 10:58:00</td><td title=\"string (19)\">2025-06-03 10:59:00</td><td title=\"string (7)\">Bandung</td><td title=\"string (1)\">2</td><td title=\"string (19)\">2025-06-03 10:58:10</td><td title=\"string (19)\">2025-06-03 10:58:10</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"integer\">1</td></tr><tr><th>11</th><td title=\"string (2)\">16</td><td title=\"string (3)\">sdf</td><td title=\"string (4)\">zxcx</td><td title=\"string (19)\">2025-06-03 14:22:00</td><td title=\"string (19)\">2025-06-03 14:25:00</td><td title=\"string (9)\">jxhfjhasd</td><td title=\"string (1)\">2</td><td title=\"string (19)\">2025-06-03 14:23:01</td><td title=\"string (19)\">2025-06-03 14:23:01</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"integer\">1</td></tr><tr><th>12</th><td title=\"string (2)\">17</td><td title=\"string (12)\">Lamine yamal</td><td title=\"string (12)\">Lamine yamal</td><td title=\"string (19)\">2025-06-09 15:23:00</td><td title=\"string (19)\">2025-06-09 15:25:00</td><td title=\"string (7)\">Bandung</td><td title=\"string (1)\">2</td><td title=\"string (19)\">2025-06-09 15:23:51</td><td title=\"string (19)\">2025-06-09 15:23:51</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"integer\">1</td></tr><tr><th>13</th><td title=\"string (2)\">18</td><td title=\"string (26)\">seminar bareng lamine fati</td><td title=\"string (26)\">seminar bareng lamine fati</td><td title=\"string (19)\">2025-06-09 21:24:00</td><td title=\"string (19)\">2025-06-09 21:26:00</td><td title=\"string (7)\">Bandung</td><td title=\"string (1)\">2</td><td title=\"string (19)\">2025-06-09 21:25:03</td><td title=\"string (19)\">2025-06-09 21:25:03</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"integer\">1</td></tr><tr><th>14</th><td title=\"string (2)\">19</td><td title=\"string (21)\">Seminar Bareng marsha</td><td title=\"string (30)\">Seminar Bareng marsha lenathea</td><td title=\"string (19)\">2025-06-11 14:50:00</td><td title=\"string (19)\">2025-06-11 15:00:00</td><td title=\"string (20)\">Metland telaga murni</td><td title=\"string (1)\">2</td><td title=\"string (19)\">2025-06-11 14:50:46</td><td title=\"string (19)\">2025-06-11 14:50:46</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"integer\">1</td></tr><tr><th>15</th><td title=\"string (2)\">21</td><td title=\"string (11)\">dgdgdgafafa</td><td title=\"string (18)\">afafafafafafafafaf</td><td title=\"string (19)\">2025-06-25 12:58:00</td><td title=\"string (19)\">2025-06-25 13:00:00</td><td title=\"string (8)\">Cikarang</td><td title=\"string (1)\">2</td><td title=\"string (19)\">2025-06-25 12:59:33</td><td title=\"string (19)\">2025-06-25 12:59:33</td><td title=\"string (19)\">Universitas Binance</td><td title=\"string (13)\">001/CERT/2024</td><td title=\"string (11)\">Ridho Fauzi</td><td title=\"string (9)\">Pembicara</td><td title=\"string (35)\">1750831173_d54c55cf716f4107aae0.jpg</td><td title=\"string (35)\">1750831173_9b869c690bc3027b7510.png</td><td title=\"integer\">1</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (22) \"Seminar Timothy ronald\"<div class=\"access-path\">$value[0]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (22) \"Seminar Timothy ronald\"<div class=\"access-path\">$value[0]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_date</dfn> =&gt; <var>string</var> (19) \"2025-05-31 21:37:00\"<div class=\"access-path\">$value[0]['start_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_date</dfn> =&gt; <var>string</var> (19) \"2025-05-31 21:41:00\"<div class=\"access-path\">$value[0]['end_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>location</dfn> =&gt; <var>string</var> (7) \"Jakarta\"<div class=\"access-path\">$value[0]['location']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_participants</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[0]['max_participants']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-31 14:35:54\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-31 14:42:06\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['institution_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>certificate_number</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['certificate_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['organizer_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_role</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['organizer_role']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_logo</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['institution_logo']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_signature</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['organizer_signature']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>current_participants</dfn> =&gt; <var>integer</var> 1<div class=\"access-path\">$value[0]['current_participants']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"6\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (30) \"seminar baerng marsha lenathea\"<div class=\"access-path\">$value[1]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (28) \"http://localhost:8080/events\"<div class=\"access-path\">$value[1]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_date</dfn> =&gt; <var>string</var> (19) \"2025-06-01 09:31:00\"<div class=\"access-path\">$value[1]['start_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_date</dfn> =&gt; <var>string</var> (19) \"2025-06-01 09:50:00\"<div class=\"access-path\">$value[1]['end_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>location</dfn> =&gt; <var>string</var> (8) \"surabaya\"<div class=\"access-path\">$value[1]['location']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_participants</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[1]['max_participants']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-01 02:30:15\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-01 02:30:15\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['institution_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>certificate_number</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['certificate_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['organizer_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_role</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['organizer_role']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_logo</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['institution_logo']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_signature</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['organizer_signature']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>current_participants</dfn> =&gt; <var>integer</var> 2<div class=\"access-path\">$value[1]['current_participants']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"7\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (22) \"sem9inar bareng marsha\"<div class=\"access-path\">$value[2]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (22) \"sem9inar bareng marsha\"<div class=\"access-path\">$value[2]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_date</dfn> =&gt; <var>string</var> (19) \"2025-06-01 10:57:00\"<div class=\"access-path\">$value[2]['start_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_date</dfn> =&gt; <var>string</var> (19) \"2025-06-01 12:00:00\"<div class=\"access-path\">$value[2]['end_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>location</dfn> =&gt; <var>string</var> (7) \"Bandung\"<div class=\"access-path\">$value[2]['location']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_participants</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[2]['max_participants']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-01 02:58:14\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-01 02:58:14\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['institution_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>certificate_number</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['certificate_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['organizer_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_role</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['organizer_role']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_logo</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['institution_logo']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_signature</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['organizer_signature']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>current_participants</dfn> =&gt; <var>integer</var> 1<div class=\"access-path\">$value[2]['current_participants']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"8\"<div class=\"access-path\">$value[3]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (18) \"seminar bareng gyj\"<div class=\"access-path\">$value[3]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (18) \"seminar bareng gyj\"<div class=\"access-path\">$value[3]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_date</dfn> =&gt; <var>string</var> (19) \"2025-06-01 20:50:00\"<div class=\"access-path\">$value[3]['start_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_date</dfn> =&gt; <var>string</var> (19) \"2025-06-01 20:59:00\"<div class=\"access-path\">$value[3]['end_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>location</dfn> =&gt; <var>string</var> (6) \"Bekasi\"<div class=\"access-path\">$value[3]['location']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_participants</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[3]['max_participants']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-01 13:50:02\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-01 13:50:02\"<div class=\"access-path\">$value[3]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['institution_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>certificate_number</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['certificate_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['organizer_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_role</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['organizer_role']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_logo</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['institution_logo']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_signature</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['organizer_signature']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>current_participants</dfn> =&gt; <var>integer</var> 1<div class=\"access-path\">$value[3]['current_participants']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"9\"<div class=\"access-path\">$value[4]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (19) \"Seminar bareng DIKA\"<div class=\"access-path\">$value[4]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (19) \"Seminar bareng DIKA\"<div class=\"access-path\">$value[4]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_date</dfn> =&gt; <var>string</var> (19) \"2025-06-01 22:06:00\"<div class=\"access-path\">$value[4]['start_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_date</dfn> =&gt; <var>string</var> (19) \"2025-06-01 22:30:00\"<div class=\"access-path\">$value[4]['end_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>location</dfn> =&gt; <var>string</var> (7) \"Bandung\"<div class=\"access-path\">$value[4]['location']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_participants</dfn> =&gt; <var>string</var> (2) \"10\"<div class=\"access-path\">$value[4]['max_participants']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-01 22:10:52\"<div class=\"access-path\">$value[4]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-01 22:10:52\"<div class=\"access-path\">$value[4]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['institution_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>certificate_number</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['certificate_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['organizer_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_role</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['organizer_role']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_logo</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['institution_logo']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_signature</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['organizer_signature']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>current_participants</dfn> =&gt; <var>integer</var> 1<div class=\"access-path\">$value[4]['current_participants']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>5</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[5]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"10\"<div class=\"access-path\">$value[5]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (32) \"Seminar bareng freya jayawardana\"<div class=\"access-path\">$value[5]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (32) \"Seminar bareng freya jayawardana\"<div class=\"access-path\">$value[5]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_date</dfn> =&gt; <var>string</var> (19) \"2025-06-03 10:42:00\"<div class=\"access-path\">$value[5]['start_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_date</dfn> =&gt; <var>string</var> (19) \"2025-06-03 10:49:00\"<div class=\"access-path\">$value[5]['end_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>location</dfn> =&gt; <var>string</var> (7) \"Bandung\"<div class=\"access-path\">$value[5]['location']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_participants</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[5]['max_participants']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-03 10:48:03\"<div class=\"access-path\">$value[5]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-03 10:48:03\"<div class=\"access-path\">$value[5]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[5]['institution_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>certificate_number</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[5]['certificate_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[5]['organizer_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_role</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[5]['organizer_role']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_logo</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[5]['institution_logo']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_signature</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[5]['organizer_signature']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>current_participants</dfn> =&gt; <var>integer</var> 1<div class=\"access-path\">$value[5]['current_participants']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>6</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[6]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"11\"<div class=\"access-path\">$value[6]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (20) \"Seminar Bareng Oline\"<div class=\"access-path\">$value[6]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (20) \"Seminar Bareng Oline\"<div class=\"access-path\">$value[6]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_date</dfn> =&gt; <var>string</var> (19) \"2025-06-03 10:50:00\"<div class=\"access-path\">$value[6]['start_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_date</dfn> =&gt; <var>string</var> (19) \"2025-06-03 10:53:00\"<div class=\"access-path\">$value[6]['end_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>location</dfn> =&gt; <var>string</var> (7) \"Bandung\"<div class=\"access-path\">$value[6]['location']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_participants</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[6]['max_participants']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-03 10:51:09\"<div class=\"access-path\">$value[6]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-03 10:51:09\"<div class=\"access-path\">$value[6]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[6]['institution_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>certificate_number</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[6]['certificate_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[6]['organizer_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_role</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[6]['organizer_role']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_logo</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[6]['institution_logo']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_signature</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[6]['organizer_signature']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>current_participants</dfn> =&gt; <var>integer</var> 1<div class=\"access-path\">$value[6]['current_participants']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>7</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[7]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"12\"<div class=\"access-path\">$value[7]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (48) \"Sertifikat hanya tersedia setelah event selesai.\"<div class=\"access-path\">$value[7]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (48) \"Sertifikat hanya tersedia setelah event selesai.\"<div class=\"access-path\">$value[7]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_date</dfn> =&gt; <var>string</var> (19) \"2025-06-03 10:51:00\"<div class=\"access-path\">$value[7]['start_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_date</dfn> =&gt; <var>string</var> (19) \"2025-06-03 10:53:00\"<div class=\"access-path\">$value[7]['end_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>location</dfn> =&gt; <var>string</var> (7) \"Bandung\"<div class=\"access-path\">$value[7]['location']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_participants</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[7]['max_participants']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-03 10:51:53\"<div class=\"access-path\">$value[7]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-03 10:51:53\"<div class=\"access-path\">$value[7]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[7]['institution_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>certificate_number</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[7]['certificate_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[7]['organizer_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_role</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[7]['organizer_role']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_logo</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[7]['institution_logo']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_signature</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[7]['organizer_signature']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>current_participants</dfn> =&gt; <var>integer</var> 1<div class=\"access-path\">$value[7]['current_participants']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>8</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[8]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"13\"<div class=\"access-path\">$value[8]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (27) \"Seminar Bareng Shani Indira\"<div class=\"access-path\">$value[8]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (27) \"Seminar Bareng Shani Indira\"<div class=\"access-path\">$value[8]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_date</dfn> =&gt; <var>string</var> (19) \"2025-06-03 10:53:00\"<div class=\"access-path\">$value[8]['start_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_date</dfn> =&gt; <var>string</var> (19) \"2025-06-03 10:55:00\"<div class=\"access-path\">$value[8]['end_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>location</dfn> =&gt; <var>string</var> (7) \"Bandung\"<div class=\"access-path\">$value[8]['location']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_participants</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[8]['max_participants']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-03 10:53:46\"<div class=\"access-path\">$value[8]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-03 10:53:46\"<div class=\"access-path\">$value[8]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[8]['institution_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>certificate_number</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[8]['certificate_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[8]['organizer_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_role</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[8]['organizer_role']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_logo</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[8]['institution_logo']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_signature</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[8]['organizer_signature']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>current_participants</dfn> =&gt; <var>integer</var> 1<div class=\"access-path\">$value[8]['current_participants']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>9</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[9]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"14\"<div class=\"access-path\">$value[9]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (28) \"Seminar Bareng Alvin alfandy\"<div class=\"access-path\">$value[9]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (28) \"Seminar Bareng Alvin alfandy\"<div class=\"access-path\">$value[9]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_date</dfn> =&gt; <var>string</var> (19) \"2025-06-03 10:56:00\"<div class=\"access-path\">$value[9]['start_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_date</dfn> =&gt; <var>string</var> (19) \"2025-06-03 10:58:00\"<div class=\"access-path\">$value[9]['end_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>location</dfn> =&gt; <var>string</var> (8) \"surabaya\"<div class=\"access-path\">$value[9]['location']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_participants</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[9]['max_participants']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-03 10:56:11\"<div class=\"access-path\">$value[9]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-03 10:56:11\"<div class=\"access-path\">$value[9]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[9]['institution_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>certificate_number</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[9]['certificate_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[9]['organizer_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_role</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[9]['organizer_role']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_logo</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[9]['institution_logo']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_signature</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[9]['organizer_signature']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>current_participants</dfn> =&gt; <var>integer</var> 1<div class=\"access-path\">$value[9]['current_participants']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>10</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[10]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[10]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (37) \"Seminar Bareng Marsha Lenathea Lapian\"<div class=\"access-path\">$value[10]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (37) \"Seminar Bareng Marsha Lenathea Lapian\"<div class=\"access-path\">$value[10]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_date</dfn> =&gt; <var>string</var> (19) \"2025-06-03 10:58:00\"<div class=\"access-path\">$value[10]['start_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_date</dfn> =&gt; <var>string</var> (19) \"2025-06-03 10:59:00\"<div class=\"access-path\">$value[10]['end_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>location</dfn> =&gt; <var>string</var> (7) \"Bandung\"<div class=\"access-path\">$value[10]['location']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_participants</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[10]['max_participants']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-03 10:58:10\"<div class=\"access-path\">$value[10]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-03 10:58:10\"<div class=\"access-path\">$value[10]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[10]['institution_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>certificate_number</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[10]['certificate_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[10]['organizer_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_role</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[10]['organizer_role']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_logo</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[10]['institution_logo']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_signature</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[10]['organizer_signature']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>current_participants</dfn> =&gt; <var>integer</var> 1<div class=\"access-path\">$value[10]['current_participants']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>11</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[11]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"16\"<div class=\"access-path\">$value[11]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (3) \"sdf\"<div class=\"access-path\">$value[11]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (4) \"zxcx\"<div class=\"access-path\">$value[11]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_date</dfn> =&gt; <var>string</var> (19) \"2025-06-03 14:22:00\"<div class=\"access-path\">$value[11]['start_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_date</dfn> =&gt; <var>string</var> (19) \"2025-06-03 14:25:00\"<div class=\"access-path\">$value[11]['end_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>location</dfn> =&gt; <var>string</var> (9) \"jxhfjhasd\"<div class=\"access-path\">$value[11]['location']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_participants</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[11]['max_participants']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-03 14:23:01\"<div class=\"access-path\">$value[11]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-03 14:23:01\"<div class=\"access-path\">$value[11]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[11]['institution_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>certificate_number</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[11]['certificate_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[11]['organizer_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_role</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[11]['organizer_role']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_logo</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[11]['institution_logo']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_signature</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[11]['organizer_signature']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>current_participants</dfn> =&gt; <var>integer</var> 1<div class=\"access-path\">$value[11]['current_participants']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>12</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[12]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"17\"<div class=\"access-path\">$value[12]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (12) \"Lamine yamal\"<div class=\"access-path\">$value[12]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (12) \"Lamine yamal\"<div class=\"access-path\">$value[12]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_date</dfn> =&gt; <var>string</var> (19) \"2025-06-09 15:23:00\"<div class=\"access-path\">$value[12]['start_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_date</dfn> =&gt; <var>string</var> (19) \"2025-06-09 15:25:00\"<div class=\"access-path\">$value[12]['end_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>location</dfn> =&gt; <var>string</var> (7) \"Bandung\"<div class=\"access-path\">$value[12]['location']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_participants</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[12]['max_participants']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-09 15:23:51\"<div class=\"access-path\">$value[12]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-09 15:23:51\"<div class=\"access-path\">$value[12]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[12]['institution_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>certificate_number</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[12]['certificate_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[12]['organizer_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_role</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[12]['organizer_role']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_logo</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[12]['institution_logo']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_signature</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[12]['organizer_signature']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>current_participants</dfn> =&gt; <var>integer</var> 1<div class=\"access-path\">$value[12]['current_participants']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>13</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[13]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"18\"<div class=\"access-path\">$value[13]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (26) \"seminar bareng lamine fati\"<div class=\"access-path\">$value[13]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (26) \"seminar bareng lamine fati\"<div class=\"access-path\">$value[13]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_date</dfn> =&gt; <var>string</var> (19) \"2025-06-09 21:24:00\"<div class=\"access-path\">$value[13]['start_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_date</dfn> =&gt; <var>string</var> (19) \"2025-06-09 21:26:00\"<div class=\"access-path\">$value[13]['end_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>location</dfn> =&gt; <var>string</var> (7) \"Bandung\"<div class=\"access-path\">$value[13]['location']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_participants</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[13]['max_participants']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-09 21:25:03\"<div class=\"access-path\">$value[13]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-09 21:25:03\"<div class=\"access-path\">$value[13]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[13]['institution_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>certificate_number</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[13]['certificate_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[13]['organizer_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_role</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[13]['organizer_role']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_logo</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[13]['institution_logo']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_signature</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[13]['organizer_signature']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>current_participants</dfn> =&gt; <var>integer</var> 1<div class=\"access-path\">$value[13]['current_participants']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>14</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[14]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"19\"<div class=\"access-path\">$value[14]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (21) \"Seminar Bareng marsha\"<div class=\"access-path\">$value[14]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (30) \"Seminar Bareng marsha lenathea\"<div class=\"access-path\">$value[14]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_date</dfn> =&gt; <var>string</var> (19) \"2025-06-11 14:50:00\"<div class=\"access-path\">$value[14]['start_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_date</dfn> =&gt; <var>string</var> (19) \"2025-06-11 15:00:00\"<div class=\"access-path\">$value[14]['end_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>location</dfn> =&gt; <var>string</var> (20) \"Metland telaga murni\"<div class=\"access-path\">$value[14]['location']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_participants</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[14]['max_participants']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-11 14:50:46\"<div class=\"access-path\">$value[14]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-11 14:50:46\"<div class=\"access-path\">$value[14]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[14]['institution_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>certificate_number</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[14]['certificate_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_name</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[14]['organizer_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_role</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[14]['organizer_role']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_logo</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[14]['institution_logo']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_signature</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[14]['organizer_signature']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>current_participants</dfn> =&gt; <var>integer</var> 1<div class=\"access-path\">$value[14]['current_participants']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>15</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[15]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"21\"<div class=\"access-path\">$value[15]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (11) \"dgdgdgafafa\"<div class=\"access-path\">$value[15]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (18) \"afafafafafafafafaf\"<div class=\"access-path\">$value[15]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_date</dfn> =&gt; <var>string</var> (19) \"2025-06-25 12:58:00\"<div class=\"access-path\">$value[15]['start_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_date</dfn> =&gt; <var>string</var> (19) \"2025-06-25 13:00:00\"<div class=\"access-path\">$value[15]['end_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>location</dfn> =&gt; <var>string</var> (8) \"Cikarang\"<div class=\"access-path\">$value[15]['location']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>max_participants</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[15]['max_participants']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-25 12:59:33\"<div class=\"access-path\">$value[15]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-25 12:59:33\"<div class=\"access-path\">$value[15]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_name</dfn> =&gt; <var>string</var> (19) \"Universitas Binance\"<div class=\"access-path\">$value[15]['institution_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>certificate_number</dfn> =&gt; <var>string</var> (13) \"001/CERT/2024\"<div class=\"access-path\">$value[15]['certificate_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_name</dfn> =&gt; <var>string</var> (11) \"Ridho Fauzi\"<div class=\"access-path\">$value[15]['organizer_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_role</dfn> =&gt; <var>string</var> (9) \"Pembicara\"<div class=\"access-path\">$value[15]['organizer_role']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>institution_logo</dfn> =&gt; <var>string</var> (35) \"1750831173_d54c55cf716f4107aae0.jpg\"<div class=\"access-path\">$value[15]['institution_logo']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>organizer_signature</dfn> =&gt; <var>string</var> (35) \"1750831173_9b869c690bc3027b7510.png\"<div class=\"access-path\">$value[15]['organizer_signature']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>current_participants</dfn> =&gt; <var>integer</var> 1<div class=\"access-path\">$value[15]['current_participants']</div></dt></dl></dd></dl></li></ul></dd></dl></div>"}}, "session": {"__ci_last_regenerate": "<pre>1750848045</pre>", "_ci_previous_url": "http://localhost:8080/events", "id": "2", "email": "<EMAIL>", "name": "admin alvin", "phone_number": "81471847148", "role": "admin", "isLoggedIn": "<pre>1</pre>", "__ci_vars": "<pre>Array\n(\n)\n</pre>"}, "headers": {"Host": "localhost:8080", "Connection": "keep-alive", "Sec-Ch-Ua": "&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "&quot;Windows&quot;", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Sec-Fetch-Site": "none", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-User": "?1", "Sec-Fetch-Dest": "document", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "en-US,en;q=0.9", "Cookie": "csrf_cookie_name=b297098787c50a56b3b296268a07a691; ci_session=fbea3d4d3911d1a801f1fa83a5ba7ab0"}, "cookies": {"csrf_cookie_name": "b297098787c50a56b3b296268a07a691", "ci_session": "fbea3d4d3911d1a801f1fa83a5ba7ab0"}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.6.1", "phpVersion": "8.2.12", "phpSAPI": "cli-server", "environment": "development", "baseURL": "http://localhost:8080", "timezone": "Asia/Jakarta", "locale": "en", "cspEnabled": false}}