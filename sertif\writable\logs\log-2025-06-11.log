DEBUG - 2025-06-11 13:28:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 13:28:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 13:29:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 13:34:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 13:34:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 13:34:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 13:35:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 13:35:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 13:35:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 13:38:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:02:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:02:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:02:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:02:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:03:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:03:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:07:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:07:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:07:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:07:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:07:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:07:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:08:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:08:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:08:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:08:19 --> Current Time for Certificate Query: 2025-06-11 14:08:19
DEBUG - 2025-06-11 14:08:19 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-11 14:08:19 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-11 14:08:19 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-11 14:08:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:08:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:08:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:08:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:08:59 --> Current Time for Certificate Query: 2025-06-11 14:08:59
DEBUG - 2025-06-11 14:08:59 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-11 14:08:59 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-11 14:08:59 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-11 14:09:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:09:02 --> User ID: 9
DEBUG - 2025-06-11 14:09:02 --> Event ID: 17
DEBUG - 2025-06-11 14:09:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:09:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:09:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:09:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:09:46 --> Current Time for Certificate Query: 2025-06-11 14:09:46
DEBUG - 2025-06-11 14:09:46 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-11 14:09:46 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-11 14:09:46 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-11 14:09:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:09:48 --> User ID: 9
DEBUG - 2025-06-11 14:09:48 --> Event ID: 17
DEBUG - 2025-06-11 14:14:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:14:54 --> User ID: 9
DEBUG - 2025-06-11 14:14:54 --> Event ID: 17
DEBUG - 2025-06-11 14:15:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:16:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:16:32 --> User ID: 9
DEBUG - 2025-06-11 14:16:32 --> Event ID: 17
DEBUG - 2025-06-11 14:17:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:17:14 --> User ID: 9
DEBUG - 2025-06-11 14:17:14 --> Event ID: 17
DEBUG - 2025-06-11 14:19:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:19:41 --> User ID: 9
DEBUG - 2025-06-11 14:19:41 --> Event ID: 17
DEBUG - 2025-06-11 14:20:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:20:18 --> User ID: 9
DEBUG - 2025-06-11 14:20:18 --> Event ID: 17
DEBUG - 2025-06-11 14:20:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:20:52 --> User ID: 9
DEBUG - 2025-06-11 14:20:52 --> Event ID: 17
DEBUG - 2025-06-11 14:20:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:20:55 --> User ID: 9
DEBUG - 2025-06-11 14:20:55 --> Event ID: 17
DEBUG - 2025-06-11 14:21:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:21:18 --> User ID: 9
DEBUG - 2025-06-11 14:21:18 --> Event ID: 17
DEBUG - 2025-06-11 14:21:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:21:52 --> User ID: 9
DEBUG - 2025-06-11 14:21:52 --> Event ID: 17
DEBUG - 2025-06-11 14:22:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:22:03 --> User ID: 9
DEBUG - 2025-06-11 14:22:03 --> Event ID: 17
DEBUG - 2025-06-11 14:22:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:22:07 --> User ID: 9
DEBUG - 2025-06-11 14:22:07 --> Event ID: 17
DEBUG - 2025-06-11 14:22:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:22:13 --> User ID: 9
DEBUG - 2025-06-11 14:22:13 --> Event ID: 17
DEBUG - 2025-06-11 14:22:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:22:18 --> User ID: 9
DEBUG - 2025-06-11 14:22:18 --> Event ID: 17
DEBUG - 2025-06-11 14:22:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:22:23 --> User ID: 9
DEBUG - 2025-06-11 14:22:23 --> Event ID: 17
DEBUG - 2025-06-11 14:23:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:37:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:37:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:37:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:37:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:37:42 --> Current Time for Certificate Query: 2025-06-11 14:37:42
DEBUG - 2025-06-11 14:37:42 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-11 14:37:42 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-11 14:37:42 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-11 14:37:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:37:44 --> User ID: 9
DEBUG - 2025-06-11 14:37:44 --> Event ID: 17
DEBUG - 2025-06-11 14:37:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:40:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:40:30 --> User ID: 9
DEBUG - 2025-06-11 14:40:30 --> Event ID: 17
DEBUG - 2025-06-11 14:40:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-11 14:41:33 --> ErrorException: Maximum execution time of 60 seconds exceeded
[Method: GET, Route: events/download-certificate/17]
in VENDORPATH\tecnickcom\tcpdf\include\tcpdf_static.php on line 1865.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
DEBUG - 2025-06-11 14:41:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-11 14:42:34 --> ErrorException: Maximum execution time of 60 seconds exceeded
[Method: GET, Route: events/download-certificate/17]
in VENDORPATH\tecnickcom\tcpdf\include\tcpdf_static.php on line 1865.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
DEBUG - 2025-06-11 14:42:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:42:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:42:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:42:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:42:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:42:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:42:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:42:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:42:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:42:40 --> Current Time for Certificate Query: 2025-06-11 14:42:40
DEBUG - 2025-06-11 14:42:40 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-11 14:42:40 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-11 14:42:40 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-11 14:42:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-11 14:43:43 --> ErrorException: Maximum execution time of 60 seconds exceeded
[Method: GET, Route: events/download-certificate/17]
in VENDORPATH\tecnickcom\tcpdf\include\tcpdf_static.php on line 1865.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
DEBUG - 2025-06-11 14:43:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:43:44 --> User ID: 9
DEBUG - 2025-06-11 14:43:44 --> Event ID: 17
DEBUG - 2025-06-11 14:44:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:44:16 --> User ID: 9
DEBUG - 2025-06-11 14:44:16 --> Event ID: 17
DEBUG - 2025-06-11 14:44:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:48:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:48:07 --> User ID: 9
DEBUG - 2025-06-11 14:48:07 --> Event ID: 17
DEBUG - 2025-06-11 14:48:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:48:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:48:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:49:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:49:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:50:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:50:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:51:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:51:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:52:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:52:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:52:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:52:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:52:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
INFO - 2025-06-11 14:52:30 --> Berhasil Daftar: <EMAIL> untuk event 19
DEBUG - 2025-06-11 14:52:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:52:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:52:38 --> Current Time for Certificate Query: 2025-06-11 14:52:38
DEBUG - 2025-06-11 14:52:38 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '10'
DEBUG - 2025-06-11 14:52:38 --> Event: Seminar Bareng marsha, EndDate: 2025-06-11 15:00:00, IsCompleted: false
DEBUG - 2025-06-11 14:52:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:52:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:52:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:52:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:52:59 --> Current Time for Certificate Query: 2025-06-11 14:52:59
DEBUG - 2025-06-11 14:52:59 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '10'
DEBUG - 2025-06-11 14:52:59 --> Event: Seminar Bareng marsha, EndDate: 2025-06-11 15:00:00, IsCompleted: false
DEBUG - 2025-06-11 14:54:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:54:02 --> Current Time for Certificate Query: 2025-06-11 14:54:02
DEBUG - 2025-06-11 14:54:02 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '10'
DEBUG - 2025-06-11 14:54:02 --> Event: Seminar Bareng marsha, EndDate: 2025-06-11 15:00:00, IsCompleted: false
DEBUG - 2025-06-11 14:54:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 14:54:04 --> Current Time for Certificate Query: 2025-06-11 14:54:04
DEBUG - 2025-06-11 14:54:04 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '10'
DEBUG - 2025-06-11 14:54:04 --> Event: Seminar Bareng marsha, EndDate: 2025-06-11 15:00:00, IsCompleted: false
DEBUG - 2025-06-11 20:01:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:02:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:03:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:03:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:03:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:03:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:03:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:03:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:03:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:15:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:16:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:16:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:16:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:19:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:19:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:20:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:20:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:20:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:20:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:20:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:20:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:20:22 --> Current Time for Certificate Query: 2025-06-11 20:20:22
DEBUG - 2025-06-11 20:20:22 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-11 20:20:22 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-11 20:20:22 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-11 20:22:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-11 20:22:07 --> CodeIgniter\Exceptions\BadMethodCallException: Call to undefined method App\Models\EventModel::getEventStats
[Method: GET, Route: reports/events]
in SYSTEMPATH\Model.php on line 930.
 1 APPPATH\Controllers\Reports.php(23): CodeIgniter\Model->__call('getEventStats', [])
 2 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Reports->events()
 3 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Reports))
 4 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 7 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 8 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-11 20:22:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:22:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-11 20:22:12 --> ErrorException: Undefined array key "name"
[Method: GET, Route: profile]
in APPPATH\Views\pages\profile\index.php on line 9.
 1 APPPATH\Views\pages\profile\index.php(9): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "name"', 'C:\\sertifikat\\backup\\myapp\\app\\Views\\pages\\profile\\index.php', 9)
 2 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\backup\\myapp\\app\\Views\\pages\\profile\\index.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/profile/index', [], true)
 5 APPPATH\Controllers\Profile.php(26): view('pages/profile/index', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Profile->index()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Profile))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-11 20:23:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:23:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:23:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:23:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:23:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:23:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:24:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:24:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:24:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:25:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-11 20:25:01 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "layouts/main.php"
[Method: GET, Route: profile]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('layouts/main.php')
 2 SYSTEMPATH\View\View.php(240): CodeIgniter\View\View->render('layouts/main', [], true)
 3 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/profile/index', [], true)
 4 APPPATH\Controllers\Profile.php(40): view('pages/profile/index', [...])
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Profile->index()
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Profile))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-11 20:25:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:25:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-11 20:25:12 --> CodeIgniter\Exceptions\BadMethodCallException: Call to undefined method App\Models\EventModel::getEventStats
[Method: GET, Route: reports/events]
in SYSTEMPATH\Model.php on line 930.
 1 APPPATH\Controllers\Reports.php(23): CodeIgniter\Model->__call('getEventStats', [])
 2 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Reports->events()
 3 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Reports))
 4 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 7 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 8 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-11 20:26:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-11 20:26:30 --> ErrorException: Undefined array key "name"
[Method: GET, Route: reports/events]
in APPPATH\Views\pages\reports\events.php on line 28.
 1 APPPATH\Views\pages\reports\events.php(28): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "name"', 'C:\\sertifikat\\backup\\myapp\\app\\Views\\pages\\reports\\events.php', 28)
 2 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\backup\\myapp\\app\\Views\\pages\\reports\\events.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/reports/events', [], true)
 5 APPPATH\Controllers\Reports.php(26): view('pages/reports/events', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Reports->events()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Reports))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-11 20:26:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:26:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:26:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:27:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:27:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-11 20:27:07 --> ErrorException: Undefined array key "name"
[Method: GET, Route: reports/events]
in APPPATH\Views\pages\reports\events.php on line 28.
 1 APPPATH\Views\pages\reports\events.php(28): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "name"', 'C:\\sertifikat\\backup\\myapp\\app\\Views\\pages\\reports\\events.php', 28)
 2 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\backup\\myapp\\app\\Views\\pages\\reports\\events.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/reports/events', [], true)
 5 APPPATH\Controllers\Reports.php(26): view('pages/reports/events', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Reports->events()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Reports))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-11 20:29:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:29:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:29:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:30:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-11 20:30:08 --> ErrorException: Undefined array key "name"
[Method: GET, Route: reports/participants]
in APPPATH\Views\pages\reports\participants.php on line 20.
 1 APPPATH\Views\pages\reports\participants.php(20): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "name"', 'C:\\sertifikat\\backup\\myapp\\app\\Views\\pages\\reports\\participants.php', 20)
 2 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\backup\\myapp\\app\\Views\\pages\\reports\\participants.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/reports/participants', [], true)
 5 APPPATH\Controllers\Reports.php(40): view('pages/reports/participants', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Reports->participants()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Reports))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-11 20:30:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:30:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:30:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:30:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:33:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:33:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:33:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:33:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:36:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:36:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:36:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:36:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:36:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:36:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:36:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:36:11 --> Current Time for Certificate Query: 2025-06-11 20:36:11
DEBUG - 2025-06-11 20:36:11 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-11 20:36:11 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-11 20:36:11 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-11 20:36:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:36:13 --> User ID: 9
DEBUG - 2025-06-11 20:36:13 --> Event ID: 17
DEBUG - 2025-06-11 20:36:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:36:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:36:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:36:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:37:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-11 20:37:01 --> ErrorException: Undefined array key "name"
[Method: GET, Route: reports/participants]
in APPPATH\Views\pages\reports\participants.php on line 20.
 1 APPPATH\Views\pages\reports\participants.php(20): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "name"', 'C:\\sertifikat\\backup\\myapp\\app\\Views\\pages\\reports\\participants.php', 20)
 2 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\backup\\myapp\\app\\Views\\pages\\reports\\participants.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/reports/participants', [], true)
 5 APPPATH\Controllers\Reports.php(40): view('pages/reports/participants', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Reports->participants()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Reports))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-11 20:37:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:37:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:37:10 --> Current Time for Certificate Query: 2025-06-11 20:37:10
DEBUG - 2025-06-11 20:37:10 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-11 20:37:10 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-11 20:37:10 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-11 20:37:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:37:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:37:13 --> Current Time for Certificate Query: 2025-06-11 20:37:13
DEBUG - 2025-06-11 20:37:13 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-11 20:37:13 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-11 20:37:13 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-11 20:37:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:37:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:37:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-11 20:37:51 --> CodeIgniter\Exceptions\BadMethodCallException: Call to undefined method App\Models\ParticipantModel::getParticipantsByEvent
[Method: GET, Route: reports/participants]
in SYSTEMPATH\Model.php on line 930.
 1 APPPATH\Controllers\Reports.php(37): CodeIgniter\Model->__call('getParticipantsByEvent', [...])
 2 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Reports->participants()
 3 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Reports))
 4 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 7 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 8 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-11 20:37:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:38:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-11 20:38:05 --> ErrorException: Undefined array key "name"
[Method: GET, Route: reports/participants]
in APPPATH\Views\pages\reports\participants.php on line 20.
 1 APPPATH\Views\pages\reports\participants.php(20): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "name"', 'C:\\sertifikat\\backup\\myapp\\app\\Views\\pages\\reports\\participants.php', 20)
 2 SYSTEMPATH\View\View.php(224): include('C:\\sertifikat\\backup\\myapp\\app\\Views\\pages\\reports\\participants.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('pages/reports/participants', [], true)
 5 APPPATH\Controllers\Reports.php(40): view('pages/reports/participants', [...])
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Reports->participants()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Reports))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-11 20:38:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:38:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-11 20:38:56 --> CodeIgniter\Exceptions\BadMethodCallException: Call to undefined method App\Models\ParticipantModel::getParticipantsByEvent
[Method: GET, Route: reports/participants]
in SYSTEMPATH\Model.php on line 930.
 1 APPPATH\Controllers\Reports.php(37): CodeIgniter\Model->__call('getParticipantsByEvent', [...])
 2 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Reports->participants()
 3 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Reports))
 4 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 7 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 8 SYSTEMPATH\rewrite.php(44): require_once('C:\\sertifikat\\backup\\myapp\\public\\index.php')
DEBUG - 2025-06-11 20:41:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:41:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:41:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:41:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:41:46 --> Current Time for Certificate Query: 2025-06-11 20:41:46
DEBUG - 2025-06-11 20:41:46 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-11 20:41:46 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-11 20:41:46 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-11 20:41:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:41:49 --> User ID: 9
DEBUG - 2025-06-11 20:41:49 --> Event ID: 17
DEBUG - 2025-06-11 20:41:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:46:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:46:58 --> User ID: 9
DEBUG - 2025-06-11 20:46:58 --> Event ID: 17
DEBUG - 2025-06-11 20:47:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:47:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:47:05 --> User ID: 9
DEBUG - 2025-06-11 20:47:05 --> Event ID: 17
DEBUG - 2025-06-11 20:47:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:47:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:47:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:47:18 --> User ID: 9
DEBUG - 2025-06-11 20:47:18 --> Event ID: 18
DEBUG - 2025-06-11 20:48:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:48:14 --> User ID: 9
DEBUG - 2025-06-11 20:48:14 --> Event ID: 18
DEBUG - 2025-06-11 20:48:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:48:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:48:32 --> User ID: 9
DEBUG - 2025-06-11 20:48:32 --> Event ID: 18
DEBUG - 2025-06-11 20:48:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:48:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:48:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:48:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:48:42 --> User ID: 9
DEBUG - 2025-06-11 20:48:42 --> Event ID: 17
DEBUG - 2025-06-11 20:48:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:57:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:57:52 --> User ID: 9
DEBUG - 2025-06-11 20:57:52 --> Event ID: 17
DEBUG - 2025-06-11 20:57:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:58:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:58:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:58:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:58:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:58:12 --> Current Time for Certificate Query: 2025-06-11 20:58:12
DEBUG - 2025-06-11 20:58:12 --> SQL Query for All Registered Events: SELECT `events`.*
FROM `participants`
JOIN `events` ON `events`.`id` = `participants`.`event_id`
WHERE `participants`.`user_id` = '9'
DEBUG - 2025-06-11 20:58:12 --> Event: Lamine yamal, EndDate: 2025-06-09 15:25:00, IsCompleted: true
DEBUG - 2025-06-11 20:58:12 --> Event: seminar bareng lamine fati, EndDate: 2025-06-09 21:26:00, IsCompleted: true
DEBUG - 2025-06-11 20:58:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:58:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-11 20:58:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
